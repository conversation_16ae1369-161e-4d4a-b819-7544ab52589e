"use client"
import { useTranslation } from "react-i18next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Head<PERSON> } from "@/components/header"
import { Shield, ArrowLeft, Calendar, MapPin, Users } from "lucide-react"
import Link from "next/link"
import "../../lib/i18n"

export default function CaseStudiesPage() {
  const { t } = useTranslation()

  const caseStudies = [
    {
      id: 1,
      title: "Modern Villa Protection System",
      category: "Residential",
      location: "Warsaw, Poland",
      date: "March 2024",
      client: "Private Residence",
      image: "/modern-luxury-villa-with-security-system-elegant-a.jpg",
      description:
        "Complete security ecosystem for a 500m² modern villa featuring perimeter protection, advanced video surveillance, and seamless smart home integration.",
      challenge:
        "The client required a comprehensive security solution that would protect their family while maintaining the aesthetic appeal of their modern architectural design.",
      solution:
        "We implemented a multi-layered Ajax security system with invisible perimeter protection, discreet indoor sensors, and strategically placed cameras that blend with the villa's design.",
      results: [
        "100% perimeter coverage without visual impact",
        "24/7 monitoring with instant mobile alerts",
        "Integration with existing smart home systems",
        "Zero false alarms in 6 months of operation",
      ],
    },
    {
      id: 2,
      title: "Office Complex Security Upgrade",
      category: "Commercial",
      location: "Krakow, Poland",
      date: "January 2024",
      client: "Tech Company HQ",
      image: "/modern-office-building-glass-facade-security-camer.jpg",
      description:
        "Multi-zone security transformation for a 3-story office building housing 200+ employees with advanced access control and fire protection systems.",
      challenge:
        "Upgrading from an outdated security system while maintaining business operations and ensuring compliance with corporate security standards.",
      solution:
        "Phased installation of Ajax systems with zone-by-zone activation, integrated access control, and comprehensive fire protection with minimal business disruption.",
      results: [
        "Seamless migration with zero downtime",
        "50% reduction in security incidents",
        "Automated access control for 200+ employees",
        "Full compliance with corporate security policies",
      ],
    },
    {
      id: 3,
      title: "Retail Chain Protection Network",
      category: "Retail",
      location: "Multiple Locations",
      date: "December 2023",
      client: "Fashion Retail Chain",
      image: "/modern-retail-store-interior-security-cameras-eleg.jpg",
      description:
        "Standardized security solution across 15 retail locations with centralized monitoring and inventory protection systems.",
      challenge:
        "Creating a unified security standard across multiple locations while addressing unique requirements of each store layout and local regulations.",
      solution:
        "Developed a modular Ajax system template that could be customized for each location while maintaining centralized monitoring and management capabilities.",
      results: [
        "85% reduction in theft incidents",
        "Centralized monitoring of all 15 locations",
        "Standardized response protocols",
        "ROI achieved within 8 months",
      ],
    },
  ]

  return (
    <div className="min-h-screen">
      <Header isTransparent={false} currentPage="case-studies" />

      <section className="py-32 bg-slate-800 relative overflow-hidden">
        <div className="absolute inset-0 hero-gradient"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        <div className="container mx-auto px-8 text-center relative z-10">
          <Link href="/">
            <Button
              variant="ghost"
              className="text-white/80 hover:text-white hover:bg-white/10 mb-12 text-sm font-light tracking-wide rounded-none group transition-all duration-300"
            >
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-300" />
              BACK TO HOME
            </Button>
          </Link>
          <h1 className="text-5xl md:text-6xl font-extralight text-white mb-8 tracking-wide text-balance">
            CASE STUDIES
          </h1>
          <div className="w-20 h-px bg-white/40 mx-auto mb-8"></div>
          <p className="text-white/80 font-light text-lg max-w-2xl mx-auto leading-relaxed">
            Real projects, real results — discover how we protect what matters most to our clients
          </p>
        </div>
      </section>

      <section className="py-32 bg-white">
        <div className="container mx-auto px-8">
          <div className="space-y-32">
            {caseStudies.map((study, index) => (
              <article key={study.id} className="max-w-4xl mx-auto group">
                <div className="aspect-[16/10] mb-12 overflow-hidden bg-gradient-to-br from-slate-100 to-blue-100/30 border border-slate-200/50">
                  <img
                    src={study.image || "/placeholder.svg"}
                    alt={study.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                  />
                </div>

                <div className="space-y-8">
                  <div className="flex flex-wrap items-center gap-4 text-sm text-slate-500 font-light">
                    <div className="flex items-center hover:text-slate-700 transition-colors">
                      <Calendar className="h-4 w-4 mr-2" />
                      {study.date}
                    </div>
                    <div className="flex items-center hover:text-slate-700 transition-colors">
                      <MapPin className="h-4 w-4 mr-2" />
                      {study.location}
                    </div>
                    <div className="flex items-center hover:text-slate-700 transition-colors">
                      <Users className="h-4 w-4 mr-2" />
                      {study.client}
                    </div>
                    <span className="px-4 py-2 bg-slate-100 text-slate-600 text-xs tracking-wider uppercase border border-slate-200/50">
                      {study.category}
                    </span>
                  </div>

                  <h2 className="text-3xl md:text-4xl font-light text-slate-800 tracking-wide text-balance">
                    {study.title}
                  </h2>

                  <p className="text-slate-600 font-light text-lg leading-relaxed">{study.description}</p>

                  <div className="grid md:grid-cols-2 gap-12 pt-8">
                    <div>
                      <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">Challenge</h3>
                      <p className="text-slate-600 font-light leading-relaxed">{study.challenge}</p>
                    </div>
                    <div>
                      <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">Solution</h3>
                      <p className="text-slate-600 font-light leading-relaxed">{study.solution}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">Results</h3>
                    <ul className="space-y-3">
                      {study.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="flex items-start group/item">
                          <div className="w-1.5 h-1.5 bg-slate-400 rounded-full mt-2.5 mr-4 flex-shrink-0 group-hover/item:bg-coral transition-colors duration-300"></div>
                          <span className="text-slate-600 font-light leading-relaxed">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {index < caseStudies.length - 1 && (
                  <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-200 to-transparent mt-20"></div>
                )}
              </article>
            ))}
          </div>
        </div>
      </section>

      <footer className="bg-slate-800 text-white py-20">
        <div className="container mx-auto px-8">
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center justify-center space-x-3 mb-6 group">
              <Shield className="h-6 w-6 text-white/80 group-hover:text-white transition-colors" />
              <span className="text-lg font-light tracking-wide group-hover:text-white/80 transition-colors">
                DEFENCE system
              </span>
            </Link>
            <div className="w-12 h-px bg-white/20 mx-auto mb-6"></div>
            <p className="text-slate-400 font-light text-sm tracking-wide mb-8">Official partner Ajax Systems</p>
          </div>
          <div className="text-center pt-8 border-t border-slate-700">
            <p className="text-slate-500 text-xs font-light tracking-wide">
              © 2024 Defence System. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
