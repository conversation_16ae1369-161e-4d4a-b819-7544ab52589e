@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens for Japanese minimalist aesthetic with muted tones */
  --background: oklch(1 0 0);
  --foreground: oklch(0.2 0 0);
  --card: oklch(0.99 0 0);
  --card-foreground: oklch(0.25 0 0);
  --popover: oklch(0.98 0 0);
  --popover-foreground: oklch(0.25 0 0);
  --primary: oklch(0.4 0.1 220);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.5 0.05 220);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.45 0 0);
  --accent: oklch(0.5 0.05 220);
  --accent-foreground: oklch(1 0 0);
  --coral: oklch(0.65 0.15 25);
  --coral-foreground: oklch(1 0 0);
  /* New gradient colors from provided image */
  --gradient-dark: #1e1e1e;
  --gradient-blue: #536d9c;
  --gradient-coral: #db4a2b;
  --gradient-brown: #a2867b;
  --gradient-light: #b8c1ca;
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0 0);
  --input: oklch(1 0 0);
  --ring: oklch(0.5 0.05 220 / 0.3);
  --chart-1: oklch(0.4 0.1 220);
  --chart-2: oklch(0.45 0.15 240);
  --chart-3: oklch(0.5 0.1 200);
  --chart-4: oklch(0.55 0.1 180);
  --chart-5: oklch(0.6 0.1 160);
  --radius: 0rem;
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.2 0 0);
  --sidebar-primary: oklch(0.4 0.1 220);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.5 0.05 220);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.92 0 0);
  --sidebar-ring: oklch(0.5 0.05 220 / 0.3);
}

.dark {
  --background: oklch(0.1 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.12 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.6 0.15 220);
  --primary-foreground: oklch(0.1 0 0);
  --secondary: oklch(0.2 0 0);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.2 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.2 0 0);
  --accent-foreground: oklch(0.95 0 0);
  --coral: oklch(0.7 0.2 25);
  --coral-foreground: oklch(0.1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.2 0 0);
  --input: oklch(0.2 0 0);
  --ring: oklch(0.4 0 0);
  --chart-1: oklch(0.6 0.15 220);
  --chart-2: oklch(0.65 0.2 240);
  --chart-3: oklch(0.7 0.15 200);
  --chart-4: oklch(0.75 0.15 180);
  --chart-5: oklch(0.8 0.15 160);
  --sidebar: oklch(0.15 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.6 0.15 220);
  --sidebar-primary-foreground: oklch(0.95 0 0);
  --sidebar-accent: oklch(0.2 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.2 0 0);
  --sidebar-ring: oklch(0.4 0 0);
}

@theme inline {
  /* Updated to use Century Gothic as primary font with proper fallbacks */
  --font-sans: "Century Gothic", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-coral: var(--coral);
  --color-coral-foreground: var(--coral-foreground);
  /* Added gradient color tokens */
  --color-gradient-dark: var(--gradient-dark);
  --color-gradient-blue: var(--gradient-blue);
  --color-gradient-coral: var(--gradient-coral);
  --color-gradient-brown: var(--gradient-brown);
  --color-gradient-light: var(--gradient-light);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: 0rem;
  --radius-md: 0rem;
  --radius-lg: 0rem;
  --radius-xl: 0rem;
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* Added Japanese minimalist typography styles */
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced font weight utilities and added smooth scrolling */
  .font-extralight {
    font-weight: 200;
  }

  .tracking-wider {
    letter-spacing: 0.1em;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Added custom gradient background utility */
  .hero-gradient {
    background-image: url("/hero-gradient.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}
