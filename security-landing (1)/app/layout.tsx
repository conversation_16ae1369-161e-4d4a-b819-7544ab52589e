import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Analytics } from "@vercel/analytics/next"
import { Suspense } from "react"
import "./globals.css"

export const metadata: Metadata = {
  title: "Defence System - Twoje <PERSON>, Nasz Priorytet",
  description:
    "Profesjonalne systemy bezpieczeństwa Ajax Systems. Ochrona antywłamaniowa, przeciwpożarowa, monitoring wideo. Oficjalny partner Ajax Systems w Polsce.",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pl">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Century+Gothic:wght@100;200;300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="font-sans antialiased">
        <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>
        <Analytics />
      </body>
    </html>
  )
}
