"use client"

import { useEffect } from "react"
import { useTranslation } from "react-i18next"
import { But<PERSON> } from "@/components/ui/button"
import { Header } from "@/components/header"
import {
  Shield,
  Eye,
  Flame,
  Droplets,
  Smartphone,
  ArrowRight,
  ExternalLink,
  CheckCircle,
  Zap,
  Users,
  Award,
} from "lucide-react"
import Link from "next/link"
import "../lib/i18n"

export default function HomePage() {
  const { t } = useTranslation()

  useEffect(() => {
    // Initialize i18n on client side
  }, [])

  return (
    <div className="min-h-screen">
      <section id="home" className="min-h-screen relative overflow-hidden">
        <Header isTransparent={true} currentPage="home" />

        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: "url(/hero-gradient.png)" }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-black/10 to-transparent"></div>

        <div className="min-h-[calc(100dvh-100px)] flex items-center justify-center">
          <div className="relative z-10 text-center text-white px-8 container mx-auto w-full">
            <div className="mb-8">
              <p className="text-sm font-light tracking-[0.2em] uppercase mb-6 opacity-90">{t("hero.partner")}</p>
              <div className="w-16 h-px bg-white/50 mx-auto mb-12"></div>
            </div>

            <h1 className="text-5xl md:text-7xl font-extralight mb-6 text-balance leading-[0.9] tracking-tight">
              {t("hero.title")}
            </h1>
            <h2 className="text-2xl md:text-4xl font-extralight mb-16 opacity-90 tracking-wide text-balance">
              {t("hero.subtitle")}
            </h2>

            <Button
              size="lg"
              className="bg-white/15 backdrop-blur-sm text-white border border-white/30 hover:bg-white/25 hover:border-white/50 transition-all duration-500 text-sm font-light tracking-wider px-12 py-6 rounded-none group"
            >
              {t("hero.cta")}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
          </div>
        </div>
      </section>

      <section id="about" className="py-32 bg-white">
        <div className="container mx-auto px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide text-balance">
              {t("about.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-3xl mx-auto leading-relaxed">{t("about.subtitle")}</p>
          </div>

          <div className="max-w-5xl mx-auto">
            <h3 className="text-2xl font-light text-slate-800 mb-12 text-center tracking-wide">
              {t("about.advantages.title")}
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              {[
                {
                  icon: Shield,
                  title: t("about.advantages.responsibility.title"),
                  description: t("about.advantages.responsibility.description"),
                },
                {
                  icon: Award,
                  title: t("about.advantages.quality.title"),
                  description: t("about.advantages.quality.description"),
                },
                {
                  icon: CheckCircle,
                  title: t("about.advantages.certified.title"),
                  description: t("about.advantages.certified.description"),
                },
                {
                  icon: Users,
                  title: t("about.advantages.knowledge.title"),
                  description: t("about.advantages.knowledge.description"),
                },
              ].map((advantage, index) => (
                <div
                  key={index}
                  className="group p-8 bg-gradient-to-br from-slate-50 to-blue-50/30 border border-slate-200/50 hover:border-slate-300/50 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <advantage.icon className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="text-lg font-light text-slate-800 mb-3 tracking-wide">{advantage.title}</h4>
                      <p className="text-slate-600 font-light text-sm leading-relaxed">{advantage.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section
        id="how-we-work"
        className="py-32 bg-gradient-to-br from-slate-50 to-blue-50/20 relative overflow-hidden"
      >
        <div className="container mx-auto px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide text-balance">
              {t("howWeWork.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed mb-6">
              {t("howWeWork.subtitle")}
            </p>
            <div className="bg-coral/10 border border-coral/20 p-6 max-w-4xl mx-auto">
              <p className="text-slate-700 font-light text-base leading-relaxed">{t("howWeWork.emphasis")}</p>
            </div>
          </div>

          <div className="flex justify-center items-center mb-24">
            <div className="relative flex items-center justify-center gap-8">
              <div className="size-48 lg:size-64 rounded-full bg-gradient-to-br from-slate-400/15 to-blue-400/25 backdrop-blur-sm border border-white/50 flex items-center justify-center relative z-10 hover:scale-105 transition-transform duration-500">
                <span className="text-slate-700 font-light text-lg tracking-wide text-center">
                  {t("howWeWork.sales")}
                </span>
              </div>
              <div className="size-56 lg:size-80 rounded-full bg-gradient-to-br from-coral/20 to-coral/30 backdrop-blur-sm border-2 border-coral/40 flex items-center justify-center relative z-20 -ml-16 hover:scale-105 transition-transform duration-500 shadow-lg">
                <span className="text-slate-800 font-medium text-xl tracking-wide text-center">
                  {t("howWeWork.installation")}
                </span>
              </div>
              <div className="size-48 lg:size-64 rounded-full bg-gradient-to-br from-slate-500/15 to-blue-500/25 backdrop-blur-sm border border-white/30 flex items-center justify-center relative z-10 -ml-16 hover:scale-105 transition-transform duration-500">
                <span className="text-slate-700 font-light text-lg tracking-wide text-center">
                  {t("howWeWork.programming")}
                </span>
              </div>
            </div>
          </div>

          {/* Process steps */}
          <div className="flex flex-wrap justify-center items-center gap-4 text-sm text-slate-600 font-light max-w-4xl mx-auto">
            {[
              t("howWeWork.steps.contact"),
              t("howWeWork.steps.consultation"),
              t("howWeWork.steps.selection"),
              t("howWeWork.steps.installation"),
              t("howWeWork.steps.configuration"),
              t("howWeWork.steps.support"),
            ].map((step, index, array) => (
              <div key={index} className="flex items-center">
                <span className="text-slate-700 tracking-wider px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40">
                  {step}
                </span>
                {index < array.length - 1 && (
                  <div className="w-4 h-px bg-gradient-to-r from-slate-300 to-slate-400 mx-2"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      <section id="products" className="py-32 bg-white">
        <div className="container mx-auto px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide">
              {t("products.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed mb-12">
              {t("products.subtitle")}
            </p>

            <div className="bg-gradient-to-br from-slate-50 to-blue-50/30 p-8 max-w-4xl mx-auto mb-16 border border-slate-200/50">
              <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">
                {t("products.systemTypes.title")}
              </h3>
              <div className="grid md:grid-cols-2 gap-6 text-left">
                <div className="flex items-start space-x-3">
                  <Zap className="h-5 w-5 text-coral mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-slate-800 mb-2">{t("products.systemTypes.wireless")}</h4>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Zap className="h-5 w-5 text-slate-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-slate-800 mb-2">{t("products.systemTypes.wired")}</h4>
                  </div>
                </div>
              </div>
              <div className="mt-6 pt-6 border-t border-slate-200">
                <p className="text-slate-600 font-light text-sm leading-relaxed">
                  {t("products.systemTypes.communication")}
                </p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: "Hub 2 Plus",
                image: "/ajax-hub-2-plus-security-system-central-unit-black.jpg",
                description: "Central control unit with advanced connectivity",
              },
              {
                name: "MotionProtect",
                image: "/ajax-motionprotect-pir-motion-detector-white-minim.jpg",
                description: "Wireless PIR motion detector with pet immunity",
              },
              {
                name: "DoorProtect",
                image: "/ajax-doorprotect-door-window-sensor-white-compact-.jpg",
                description: "Door and window opening detector",
              },
              {
                name: "FireProtect",
                image: "/ajax-fireprotect-smoke-detector-white-ceiling-moun.jpg",
                description: "Wireless smoke and temperature detector",
              },
              {
                name: "LeaksProtect",
                image: "/ajax-leaksprotect-water-leak-detector-white-compac.jpg",
                description: "Wireless flood detector with temperature sensor",
              },
              {
                name: "StreetSiren",
                image: "/ajax-streetsiren-outdoor-wireless-siren-black-weat.jpg",
                description: "Outdoor wireless siren with LED indication",
              },
            ].map((product, index) => (
              <div key={index} className="group h-full">
                <div className="bg-gradient-to-br from-slate-50 to-blue-50/30 p-8 border border-slate-200/50 hover:border-slate-300/50 transition-all duration-300 h-full flex flex-col">
                  <div className="aspect-square mb-6 overflow-hidden">
                    <img
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                  <div className="flex-1 flex flex-col">
                    <h3 className="text-xl font-light text-slate-800 mb-3 tracking-wide">{product.name}</h3>
                    <p className="text-slate-600 font-light text-sm leading-relaxed line-clamp-3">
                      {product.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section id="portfolio" className="py-32 bg-gradient-to-br from-slate-50/50 to-blue-50/20">
        <div className="container mx-auto px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide">
              {t("portfolio.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed">
              {t("portfolio.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto mb-16">
            {[
              {
                title: t("portfolio.projects.villa.title"),
                category: t("portfolio.categories.residential"),
                description: t("portfolio.projects.villa.description"),
                image: "/modern-villa-with-security-system-installation-ele.jpg",
              },
              {
                title: t("portfolio.projects.office.title"),
                category: t("portfolio.categories.commercial"),
                description: t("portfolio.projects.office.description"),
                image: "/modern-office-building-with-security-cameras-and-a.jpg",
              },
            ].map((study, index) => (
              <div key={index} className="group">
                <div className="aspect-[3/2] mb-6 overflow-hidden bg-gradient-to-br from-slate-100 to-blue-100/30">
                  <img
                    src={study.image || "/placeholder.svg"}
                    alt={study.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                  />
                </div>
                <div className="space-y-3">
                  <div className="text-xs font-light tracking-wider uppercase text-slate-500">{study.category}</div>
                  <h3 className="text-2xl font-light text-slate-800 tracking-wide">{study.title}</h3>
                  <p className="text-slate-600 font-light leading-relaxed">{study.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link href="/case-studies">
              <Button
                variant="outline"
                className="bg-transparent border-slate-300 text-slate-700 hover:bg-slate-50 hover:border-slate-400 transition-all duration-300 text-sm font-light tracking-wider px-8 py-3 rounded-none group"
              >
                {t("portfolio.viewAll")}
                <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="py-24 bg-white">
        <div className="container mx-auto px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-light text-slate-800 mb-4 tracking-wide">{t("forWhom.title")}</h2>
            <div className="w-12 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light max-w-2xl mx-auto leading-relaxed">{t("forWhom.subtitle")}</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[
              t("forWhom.homes"),
              t("forWhom.offices"),
              t("forWhom.shops"),
              t("forWhom.developments"),
              t("forWhom.areas"),
            ].map((item, index) => (
              <div key={index} className="group">
                <div className="h-px bg-slate-200 group-hover:bg-coral transition-colors duration-300 mb-4"></div>
                <p className="text-slate-600 font-light text-sm leading-relaxed">{item}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-24 bg-gradient-to-br from-slate-50/50 to-blue-50/20">
        <div className="container mx-auto px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-6xl mx-auto">
            {/* Anti-burglary Protection */}
            <div className="group">
              <div className="flex items-center mb-6">
                <Shield className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mr-4" />
                <h3 className="text-lg font-light text-slate-800 tracking-wide">
                  {t("protection.antiburglary.title")}
                </h3>
              </div>
              <div className="space-y-3 text-slate-600 font-light text-sm leading-relaxed">
                <p>{t("protection.antiburglary.motion")}</p>
                <p>{t("protection.antiburglary.doors")}</p>
                <p>{t("protection.antiburglary.tilt")}</p>
                <p>{t("protection.antiburglary.glass")}</p>
              </div>
            </div>

            {/* Fire Protection */}
            <div className="group">
              <div className="flex items-center mb-6">
                <Flame className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mr-4" />
                <h3 className="text-lg font-light text-slate-800 tracking-wide">{t("protection.fire.title")}</h3>
              </div>
              <div className="space-y-3 text-slate-600 font-light text-sm leading-relaxed">
                <p>{t("protection.fire.smoke")}</p>
                <p>{t("protection.fire.temperature")}</p>
                <p>{t("protection.fire.sirens")}</p>
              </div>
            </div>

            {/* Flood Protection */}
            <div className="group">
              <div className="flex items-center mb-6">
                <Droplets className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mr-4" />
                <h3 className="text-lg font-light text-slate-800 tracking-wide">{t("protection.flood.title")}</h3>
              </div>
              <div className="space-y-3 text-slate-600 font-light text-sm leading-relaxed">
                <p>{t("protection.flood.leak")}</p>
                <p>{t("protection.flood.valve")}</p>
              </div>
            </div>

            {/* Video Monitoring */}
            <div className="group">
              <div className="flex items-center mb-6">
                <Eye className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mr-4" />
                <h3 className="text-lg font-light text-slate-800 tracking-wide">{t("protection.video.title")}</h3>
              </div>
              <div className="space-y-3 text-slate-600 font-light text-sm leading-relaxed">
                <p>{t("protection.video.cameras")}</p>
                <p>{t("protection.video.ai")}</p>
                <p>{t("protection.video.recording")}</p>
              </div>
            </div>

            {/* Integration */}
            <div className="group md:col-span-2 lg:col-span-2">
              <div className="flex items-center mb-6">
                <Smartphone className="h-6 w-6 text-slate-600 group-hover:text-coral transition-colors duration-300 mr-4" />
                <h3 className="text-lg font-light text-slate-800 tracking-wide">{t("protection.integration.title")}</h3>
              </div>
              <div className="space-y-3 text-slate-600 font-light text-sm leading-relaxed">
                <p>{t("protection.integration.push")}</p>
                <p>{t("protection.integration.remote")}</p>
                <p>{t("protection.integration.security")}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="contact" className="py-24 bg-slate-900 text-white">
        <div className="container mx-auto px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-start">
              {/* Contact Form */}
              <div>
                <h2 className="text-3xl font-light mb-8 tracking-wide">{t("contact.subtitle")}</h2>

                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <input
                        type="text"
                        placeholder={t("contact.name")}
                        className="w-full bg-transparent border-b border-white/30 pb-3 text-white placeholder-white/60 focus:border-white/60 focus:outline-none transition-colors"
                      />
                    </div>
                    <div>
                      <input
                        type="email"
                        placeholder={t("contact.email")}
                        className="w-full bg-transparent border-b border-white/30 pb-3 text-white placeholder-white/60 focus:border-white/60 focus:outline-none transition-colors"
                      />
                    </div>
                  </div>

                  <div>
                    <textarea
                      placeholder={t("contact.message")}
                      rows={6}
                      className="w-full bg-transparent border border-white/30 p-4 text-white placeholder-white/60 focus:border-white/60 focus:outline-none transition-colors resize-none"
                    ></textarea>
                  </div>

                  <Button className="bg-coral hover:bg-coral/80 text-white px-12 py-3 rounded-none font-light tracking-wide transition-colors">
                    {t("contact.submit")}
                  </Button>
                </form>
              </div>

              {/* Contact Info */}
              <div className="lg:pl-12">
                <h3 className="text-2xl font-light mb-8 tracking-wide">{t("contact.title")}</h3>

                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <div className="w-2 h-2 bg-coral rounded-full"></div>
                    </div>
                    <span className="text-white/80 font-light">{t("contact.info.phone")}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <div className="w-2 h-2 bg-coral rounded-full"></div>
                    </div>
                    <span className="text-white/80 font-light">{t("contact.info.email")}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <div className="w-2 h-2 bg-coral rounded-full"></div>
                    </div>
                    <span className="text-white/80 font-light">{t("contact.info.telegram")}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <footer className="bg-slate-800 text-white py-16">
        <div className="container mx-auto px-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <Shield className="h-6 w-6 text-white/80" />
              <span className="text-lg font-light tracking-wide">DEFENCE system</span>
            </div>
            <div className="w-12 h-px bg-white/20 mx-auto mb-6"></div>
            <p className="text-slate-400 font-light text-sm tracking-wide mb-6">Official partner Ajax Systems</p>
          </div>
          <div className="text-center pt-8 border-t border-slate-700">
            <p className="text-slate-500 text-xs font-light tracking-wide">
              © 2024 Defence System. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
