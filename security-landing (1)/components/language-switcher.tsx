"use client"

import { useTranslation } from "react-i18next"
import { But<PERSON> } from "@/components/ui/button"

export function LanguageSwitcher() {
  const { i18n } = useTranslation()

  const toggleLanguage = () => {
    const newLang = i18n.language === "pl" ? "en" : "pl"
    i18n.changeLanguage(newLang)
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="bg-transparent border-0 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 text-xs font-light tracking-wider px-3 py-2 rounded-none"
    >
      {i18n.language === "pl" ? "EN" : "PL"}
    </Button>
  )
}
