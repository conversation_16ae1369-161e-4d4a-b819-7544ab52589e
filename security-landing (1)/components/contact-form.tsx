"use client"

import type React from "react"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Shield, Mail, Phone, User, MessageSquare } from "lucide-react"

export function ContactForm() {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }))
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-16">
        <div className="flex items-center justify-center space-x-3 mb-6">
          <Shield className="h-6 w-6 text-slate-600" />
          <h2 className="text-3xl font-light text-slate-800 tracking-wide">{t("contact.title")}</h2>
        </div>
        <div className="w-12 h-px bg-coral mx-auto mb-6"></div>
        <p className="text-slate-600 font-light leading-relaxed">
          Skontaktuj się z nami, aby uzyskać bezpłatną konsultację i wycenę systemu bezpieczeństwa.
        </p>
      </div>

      <div className="bg-white/80 backdrop-blur-sm border border-slate-200/50 p-12">
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="relative">
            <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
            <Input
              name="name"
              placeholder={t("contact.name")}
              value={formData.name}
              onChange={handleChange}
              className="pl-12 h-14 bg-white/50 border-slate-200 focus:border-coral focus:ring-coral/20 text-slate-700 font-light tracking-wide rounded-none"
              required
            />
          </div>

          <div className="relative">
            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
            <Input
              name="email"
              type="email"
              placeholder={t("contact.email")}
              value={formData.email}
              onChange={handleChange}
              className="pl-12 h-14 bg-white/50 border-slate-200 focus:border-coral focus:ring-coral/20 text-slate-700 font-light tracking-wide rounded-none"
              required
            />
          </div>

          <div className="relative">
            <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
            <Input
              name="phone"
              type="tel"
              placeholder={t("contact.phone")}
              value={formData.phone}
              onChange={handleChange}
              className="pl-12 h-14 bg-white/50 border-slate-200 focus:border-coral focus:ring-coral/20 text-slate-700 font-light tracking-wide rounded-none"
            />
          </div>

          <div className="relative">
            <MessageSquare className="absolute left-4 top-6 h-5 w-5 text-slate-400" />
            <Textarea
              name="message"
              placeholder={t("contact.message")}
              value={formData.message}
              onChange={handleChange}
              rows={6}
              className="pl-12 pt-5.5 bg-white/50 border-slate-200 focus:border-coral focus:ring-coral/20 text-slate-700 font-light tracking-wide rounded-none"
              required
            />
          </div>

          <div className="text-center pt-4">
            <Button
              type="submit"
              size="lg"
              className="bg-coral hover:bg-coral/90 text-white border-0 px-12 py-4 text-sm font-light tracking-wide rounded-none transition-all duration-300 hover:shadow-lg"
            >
              {t("contact.submit")}
            </Button>
          </div>
        </form>

        <div className="mt-12 pt-8 border-t border-slate-200/50 text-center">
          <p className="text-slate-500 font-light text-sm tracking-wide">Odpowiemy w ciągu 24 godzin</p>
        </div>
      </div>
    </div>
  )
}
