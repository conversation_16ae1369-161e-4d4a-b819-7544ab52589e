import i18n from "i18next"
import { initReactI18next } from "react-i18next"

const resources = {
  pl: {
    translation: {
      nav: {
        home: "Strona główna",
        about: "O nas",
        howWeWork: "Jak pracujemy",
        products: "Produkty",
        portfolio: "Portfolio",
        contact: "Konta<PERSON>",
      },
      hero: {
        title: "TWOJE BEZPIECZEŃSTWO",
        subtitle: "NASZ PRIORYTET",
        partner: "OFICJALNY PARTNER AJAX SYSTEMS",
        cta: "UMÓW KONSULTACJĘ",
      },
      about: {
        title: "O NAS",
        subtitle:
          "Jesteśmy Defense System, oficjalny partner Ajax Systems. Nasza misja to zapewnienie klientom maksymalnego poziomu bezpieczeństwa i spokoju dzięki profesjonalnie wdrożonym rozwiązaniom.",
        advantages: {
          title: "Nasze przewagi",
          responsibility: {
            title: "Pełna odpowiedzialność",
            description: "Bierzemy system bezpieczeństwa pod klucz",
          },
          quality: {
            title: "Gwarantowana jakość",
            description: "<PERSON><PERSON><PERSON><PERSON><PERSON> gruntownie, z precyzją w szczegółach",
          },
          certified: {
            title: "Certyfikowani",
            description: "W naszym zespole są certyfikowani elektrycy i specjaliści od systemów bezpieczeństwa",
          },
          knowledge: {
            title: "Znajomość produktu",
            description: "Specjalizacja właśnie w Ajax, pełne zrozumienie wszystkich możliwości systemu",
          },
        },
      },
      howWeWork: {
        title: "JAK PRACUJEMY",
        subtitle:
          "Sprzedaż – Montaż – Programowanie. Wszystkie obiekty które potrzebują bezpieczeństwa, nawet Twój letni kamper.",
        sales: "SPRZEDAŻ",
        installation: "MONTAŻ",
        programming: "PROGRAMOWANIE",
        emphasis:
          "W Warszawie jest tylko 4-6 firm takich jak my. Oferujemy kompleks: produkcja to jedno, ale montaż i programowanie prawie nikt nie proponuje.",
        steps: {
          contact: "KONTAKT",
          consultation: "KONSULTACJA I PRZYJAZD",
          selection: "DOBÓR SYSTEMU",
          installation: "MONTAŻ",
          configuration: "KONFIGURACJA",
          support: "WSPARCIE",
        },
      },
      products: {
        title: "SYSTEMY OCHRONY",
        subtitle:
          "Dzielimy na kategorie – antywłamaniowe, przeciwzalaniowe, przeciwpożarowe, komfort, automatyzacja, wideonadzór",
        systemTypes: {
          title: "Typy systemów",
          wireless: "System bezprzewodowy",
          wired: "System przewodowy",
          communication: "Informacje o typach kanałów komunikacji nawet w ekstremalnych przypadkach",
        },
      },
      portfolio: {
        title: "PORTFOLIO",
        subtitle: "Nasze realizacje – zobacz jak chronimy to, co najważniejsze",
        viewAll: "ZOBACZ WSZYSTKIE PROJEKTY",
        categories: {
          residential: "Mieszkalnictwo",
          commercial: "Komercyjne",
        },
        projects: {
          villa: {
            title: "Ochrona Nowoczesnej Willi",
            description:
              "Kompletna instalacja systemu bezpieczeństwa dla willi o powierzchni 500m² obejmująca ochronę obwodową, monitoring wideo i integrację z inteligentnym domem.",
          },
          office: {
            title: "Bezpieczeństwo Kompleksu Biurowego",
            description:
              "Wielostrefowy system bezpieczeństwa dla 3-piętrowego budynku biurowego z kontrolą dostępu, ochroną przeciwpożarową i integracją z monitoringiem 24/7.",
          },
        },
      },
      forWhom: {
        title: "Dla kogo?",
        subtitle: "Nasze systemy montujemy wszędzie tam, gdzie liczy się bezpieczeństwo:",
        homes: "Domy i mieszkania – pełna ochrona rodziny i majątku.",
        offices: "Biura i firmy – kontrola dostępu, monitoring i bezpieczeństwo pracowników.",
        shops: "Sklepy, salony, restauracje – ochrona towaru i całodobowy podgląd.",
        developments: "Inwestycje deweloperskie – kompleksowe systemy dla budynków i osiedli.",
        areas: "Tereny i parkingi – kamery, analiza obrazu (AI), ochrona obiektów zewnętrznych.",
      },
      protection: {
        antiburglary: {
          title: "Ochrona antywłamaniowa",
          motion: "czujniki ruchu",
          doors: "czujniki otwarcia drzwi i okien",
          tilt: "czujniki pochylenia okien",
          glass: "czujniki zbicia szyby",
        },
        fire: {
          title: "Ochrona przeciwpożarowa",
          smoke: "czujniki dymu",
          temperature: "czujniki temperatury",
          sirens: "syreny wewnętrzne i zewnętrzne",
        },
        flood: {
          title: "Ochrona przeciwzalaniowa",
          leak: "czujniki wycieku wody",
          valve: "automatyczny zawór odcinający wodę",
        },
        video: {
          title: "Monitoring wideo",
          cameras: "kamery IP z podglądem na żywo w aplikacji",
          ai: "sztuczna inteligencja: rozpoznawanie osoby, pojazdu, zwierzęcia",
          recording: "zapis i archiwizacja nagrań",
        },
        integration: {
          title: "Integracja i powiadomienia",
          push: "powiadomienia push 24/7",
          remote: "zdalny dostęp przez aplikację",
          security: "podłączenie do agencji ochrony",
        },
      },
      cta: {
        title: "Umów się na bezpłatną konsultację – przyjedziemy i doradzimy.",
        button: "Skontaktuj się z nami",
      },
      contact: {
        title: "KONTAKT",
        subtitle: "Skontaktuj się z nami",
        name: "Imię i nazwisko",
        email: "Email",
        phone: "Telefon",
        message: "Wiadomość",
        submit: "Wyślij",
        info: {
          phone: "0 800 331 911",
          email: "<EMAIL>",
          telegram: "@AjaxSystemsSupport_Bot",
        },
        office: {
          title: "Nasze Biuro",
          address: "Cybernetyki 12, 02-677 Warszawa",
          mapTitle: "Lokalizacja biura Defence System",
        },
      },
    },
  },
  en: {
    translation: {
      nav: {
        home: "Home",
        about: "About us",
        howWeWork: "How we work",
        products: "Products",
        portfolio: "Portfolio",
        contact: "Contact",
      },
      hero: {
        title: "YOUR SAFETY",
        subtitle: "OUR PRIORITY",
        partner: "OFFICIAL PARTNER AJAX SYSTEMS",
        cta: "GET CONSULTATION",
      },
      about: {
        title: "ABOUT US",
        subtitle:
          "We are Defense System, official partner of Ajax Systems. Our mission is to provide clients with maximum level of security and peace of mind through professionally implemented solutions.",
        advantages: {
          title: "Our advantages",
          responsibility: {
            title: "Full responsibility",
            description: "We take the security system turnkey",
          },
          quality: {
            title: "Guaranteed quality",
            description: "We work thoroughly, with precision in details",
          },
          certified: {
            title: "Certified",
            description: "Our team includes certified electricians and security system specialists",
          },
          knowledge: {
            title: "Product knowledge",
            description: "Specialization specifically in Ajax, full understanding of all system capabilities",
          },
        },
      },
      howWeWork: {
        title: "HOW WE WORK",
        subtitle: "Sales – Installation – Programming. All objects that need security, even your summer camper.",
        sales: "SALES",
        installation: "INSTALLATION",
        programming: "PROGRAMMING",
        emphasis:
          "In Warsaw there are only 4-6 companies like us. We offer a complete package: production is one thing, but installation and programming almost no one offers.",
        steps: {
          contact: "CONTACT",
          consultation: "CONSULTATION & VISIT",
          selection: "SYSTEM SELECTION",
          installation: "INSTALLATION",
          configuration: "CONFIGURATION",
          support: "ONGOING SUPPORT",
        },
      },
      products: {
        title: "SECURITY SYSTEMS",
        subtitle:
          "We divide into categories – anti-burglary, flood protection, fire protection, comfort, automation, video surveillance",
        systemTypes: {
          title: "System types",
          wireless: "Wireless system",
          wired: "Wired system",
          communication: "Information about communication channel types even in extreme cases",
        },
      },
      portfolio: {
        title: "PORTFOLIO",
        subtitle: "Our implementations – see how we protect what matters most",
        viewAll: "VIEW ALL PROJECTS",
        categories: {
          residential: "Residential",
          commercial: "Commercial",
        },
        projects: {
          villa: {
            title: "Modern Villa Protection",
            description:
              "Complete security system installation for a 500m² villa including perimeter protection, video surveillance, and smart home integration.",
          },
          office: {
            title: "Office Complex Security",
            description:
              "Multi-zone security system for a 3-story office building with access control, fire protection, and 24/7 monitoring integration.",
          },
        },
      },
      forWhom: {
        title: "For whom?",
        subtitle: "We install our systems everywhere security matters:",
        homes: "Homes and apartments – complete protection of family and property.",
        offices: "Offices and companies – access control, monitoring and employee security.",
        shops: "Shops, salons, restaurants – goods protection and 24/7 monitoring.",
        developments: "Development investments – comprehensive systems for buildings and housing estates.",
        areas: "Areas and parking lots – cameras, image analysis (AI), protection of external objects.",
      },
      protection: {
        antiburglary: {
          title: "Anti-burglary protection",
          motion: "motion sensors",
          doors: "door and window opening sensors",
          tilt: "window tilt sensors",
          glass: "glass break sensors",
        },
        fire: {
          title: "Fire protection",
          smoke: "smoke detectors",
          temperature: "temperature sensors",
          sirens: "internal and external sirens",
        },
        flood: {
          title: "Flood protection",
          leak: "water leak sensors",
          valve: "automatic water shut-off valve",
        },
        video: {
          title: "Video monitoring",
          cameras: "IP cameras with live view in the app",
          ai: "artificial intelligence: person, vehicle, animal recognition",
          recording: "recording and archiving of footage",
        },
        integration: {
          title: "Integration and notifications",
          push: "push notifications 24/7",
          remote: "remote access via app",
          security: "connection to security agency",
        },
      },
      cta: {
        title: "Schedule a free consultation – we'll come and advise.",
        button: "Contact us",
      },
      contact: {
        title: "CONTACT",
        subtitle: "Contact us",
        name: "Full name",
        email: "Email",
        phone: "Phone",
        message: "Message",
        submit: "Send",
        info: {
          phone: "0 800 331 911",
          email: "<EMAIL>",
          telegram: "@AjaxSystemsSupport_Bot",
        },
        office: {
          title: "Our Office",
          address: "Cybernetyki 12, 02-677 Warsaw",
          mapTitle: "Defence System office location",
        },
      },
    },
  },
}

i18n.use(initReactI18next).init({
  resources,
  lng: "pl",
  fallbackLng: "pl",
  interpolation: {
    escapeValue: false,
  },
})

export default i18n
