# Security Landing Page - Setup Guide

This is a production-ready Next.js website with Payload CMS integration for a security company.

## Node.js Version Requirements

**CRITICAL**: This project requires Node.js version:

- `^18.18.0 || ^19.8.0 || >= 20.0.0` for Next.js
- `^18.19.0 || ^20.6.0` for Payload CMS

**Current Issue**: You are running Node.js v19.7.0, which is not compatible.

### Solution Options:

#### Option 1: Upgrade Node.js (Recommended)

```bash
# Using nvm (Node Version Manager)
nvm install 20
nvm use 20

# Or install Node.js 20 LTS directly from nodejs.org
```

#### Option 2: Use Node.js 19.8.0+

```bash
nvm install 19.8.0
nvm use 19.8.0
```

## Project Setup

1. **Install Dependencies**

   ```bash
   pnpm install
   ```

2. **Generate Payload Types** (after Node.js upgrade)

   ```bash
   pnpm generate:types
   ```

   Note: If this fails due to Node.js version, the project uses custom types in `custom-payload-types.ts`

3. **Development Server**

   ```bash
   pnpm dev
   ```

4. **Production Build**
   ```bash
   pnpm build
   pnpm start
   ```

## Features Implemented

✅ **Payload CMS Integration**

- Direct Payload SDK usage (no API routes)
- Auto-generated TypeScript types
- Server-side data fetching
- Collections: Translations, SEO, Case Studies, Contacts

✅ **Multilingual Support**

- Polish and English translations
- Dynamic translation loading from Payload
- react-i18next integration

✅ **SEO Management**

- Dynamic meta tags from Payload CMS
- Open Graph and Twitter Cards
- Structured data (JSON-LD)
- Dynamic sitemap and robots.txt

✅ **Case Studies**

- Featured case studies on homepage
- Dedicated case studies page
- Rich content with images
- Category and location filtering

✅ **Contact Form**

- Direct submission to Payload CMS
- Email validation
- Status tracking and priority assignment

✅ **Production Ready**

- Error boundaries
- Caching system
- Security headers
- Analytics integration (Google Analytics)
- Performance optimizations

## Payload CMS Collections

### Translations

- Key-value pairs for multilingual text
- Polish and English support
- Category organization

### SEO

- Page-specific meta data
- Keywords, descriptions, OG images
- Canonical URLs and indexing control

### Case Studies

- Rich content with images
- Category classification
- Client information and results
- Publish/draft status

### Contacts

- Form submissions storage
- Status and priority tracking
- Assignment and follow-up management

## File Structure

```
/
├── app/
│   ├── (app)/              # Main application routes
│   ├── (payload)/          # Payload admin interface
│   ├── sitemap.ts          # Dynamic sitemap
│   └── robots.ts           # Dynamic robots.txt
├── collections/            # Payload CMS collections
├── components/             # React components
├── lib/
│   ├── payload.ts          # Payload SDK functions
│   ├── payload-server.ts   # Server-side Payload functions
│   ├── cache.ts           # Caching utilities
│   └── i18n.ts            # Internationalization config
├── custom-payload-types.ts # Custom types (fallback)
└── payload.config.ts      # Payload CMS configuration
```

## Environment Variables

Create a `.env.local` file:

```env
# Database
DATABASE_URI=mongodb://localhost:27017/security-landing

# Payload
PAYLOAD_SECRET=your-secret-key

# Analytics (optional)
NEXT_PUBLIC_GA_ID=GA_MEASUREMENT_ID

# Production URL
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

## Known Issues & Workarounds

### Node.js Version Compatibility

- **Issue**: Cannot generate Payload types with Node.js v19.7.0
- **Workaround**: Using `custom-payload-types.ts` with manually defined types
- **Fix**: Upgrade to Node.js 20 LTS and run `pnpm generate:types`

### Type Generation

- After Node.js upgrade, regenerate types:
  ```bash
  pnpm generate:types
  ```
- Then update imports in `lib/payload.ts` and `lib/payload-server.ts`:
  ```typescript
  // Change from:
  import type { ... } from "@/custom-payload-types";
  // To:
  import type { ... } from "@/payload-types";
  ```

## Production Deployment

1. **Upgrade Node.js** to compatible version
2. **Generate types**: `pnpm generate:types`
3. **Build**: `pnpm build`
4. **Deploy** with your preferred platform (Vercel, Docker, etc.)

## Security Features

- Content Security Policy headers
- XSS protection
- Frame options
- HTTPS redirects
- Input validation and sanitization

## Performance Features

- Image optimization with Next.js
- Caching for Payload queries
- Static generation where possible
- Compression and minification
