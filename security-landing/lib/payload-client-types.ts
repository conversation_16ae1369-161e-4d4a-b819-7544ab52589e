// Client-side types only (no server imports)
// This file provides types for client components without importing server-side payload config

export type PayloadTranslation = {
  id: string;
  key: string;
  polish: string;
  english: string;
  description?: string;
  category?: string;
};

export type PayloadSEO = {
  id: string;
  slug: string;
  language: "pl" | "en";
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: {
    url: string;
    alt?: string;
  };
  canonicalUrl?: string;
  robots?: string;
  priority?: number;
  changeFreq?: string;
};

export type PayloadCaseStudy = {
  id: string;
  title: string;
  slug: string;
  language: "pl" | "en";
  excerpt: string;
  content: any; // Rich text content
  coverImage?: {
    url: string;
    alt?: string;
  };
  gallery?: Array<{
    url: string;
    alt?: string;
  }>;
  date: string;
  status: "draft" | "published";
  tags?: string[];
  client?: string;
  location?: string;
  duration?: string;
  teamSize?: number;
  technology?: string[];
  challenge?: string;
  solution?: string;
  results?: string;
  testimonial?: {
    quote: string;
    author: string;
    position: string;
  };
};

export type PayloadContact = {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  status: "new" | "in-progress" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  source: "website" | "phone" | "email" | "referral";
  createdAt: string;
  updatedAt: string;
};

export type PayloadMedia = {
  id: string;
  url: string;
  filename: string;
  alt?: string;
  mimeType?: string;
  filesize?: number;
  width?: number;
  height?: number;
  sizes?: {
    thumbnail?: {
      url: string;
      width: number;
      height: number;
    };
    medium?: {
      url: string;
      width: number;
      height: number;
    };
    large?: {
      url: string;
      width: number;
      height: number;
    };
  };
};

// Helper function for client-side structured data generation
export function generateStructuredData(
  type: "website" | "organization" | "article",
  data: any
) {
  const baseUrl = process.env.NEXT_PUBLIC_URL || "https://defencesystem.pl";

  const commonData = {
    "@context": "https://schema.org",
  };

  switch (type) {
    case "website":
      return {
        ...commonData,
        "@type": "WebSite",
        name: data.name || "Defence System",
        description: data.description,
        url: baseUrl,
        potentialAction: {
          "@type": "SearchAction",
          target: `${baseUrl}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string",
        },
      };

    case "organization":
      return {
        ...commonData,
        "@type": "Organization",
        name: "Defence System",
        description: "Profesjonalne systemy bezpieczeństwa Ajax Systems",
        url: baseUrl,
        logo: `${baseUrl}/placeholder-logo.png`,
        contactPoint: {
          "@type": "ContactPoint",
          telephone: "+48-XXX-XXX-XXX",
          contactType: "customer service",
          availableLanguage: ["Polish", "English"],
        },
        address: {
          "@type": "PostalAddress",
          streetAddress: "Cybernetyki 12",
          addressLocality: "Warsaw",
          postalCode: "02-677",
          addressCountry: "PL",
        },
      };

    case "article":
      return {
        ...commonData,
        "@type": "Article",
        headline: data.title,
        description: data.description,
        author: {
          "@type": "Organization",
          name: "Defence System",
        },
        publisher: {
          "@type": "Organization",
          name: "Defence System",
          logo: {
            "@type": "ImageObject",
            url: `${baseUrl}/placeholder-logo.png`,
          },
        },
        datePublished: data.datePublished,
        dateModified: data.dateModified || data.datePublished,
      };

    default:
      return commonData;
  }
}

