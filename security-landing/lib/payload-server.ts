// Server-side Payload functions using Local API
// These functions should only be used in server components or server actions

"use server";

import { getPayload } from "payload";
import config from "@/payload.config";
import type {
  Translation,
  Seo,
  CaseStudy,
  Contact,
} from "@/custom-payload-types";

// Get cached Payload instance
let payloadInstance: any = null;

async function getPayloadInstance() {
  if (!payloadInstance) {
    payloadInstance = await getPayload({ config });
  }
  return payloadInstance;
}

// Server action to create contact from FormData
export async function createContactAction(formData: FormData) {
  try {
    const payload = await getPayloadInstance();

    const fullName = formData.get("fullName") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;
    const message = formData.get("message") as string;

    if (!fullName || !email || !message) {
      throw new Error("Missing required fields");
    }

    const contact = await payload.create({
      collection: "contacts",
      data: {
        fullName,
        email,
        phone: phone || undefined,
        message,
        subject: `New contact inquiry from ${fullName}`,
        status: "new",
        priority: "medium",
        source: "website",
      },
    });

    return { success: true, contact };
  } catch (error) {
    console.error("Error creating contact:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Server action to create contact from object data
export async function createContactFromObject(data: {
  fullName: string;
  email: string;
  phone?: string;
  message: string;
  subject?: string;
}) {
  try {
    const payload = await getPayloadInstance();

    const contact = await payload.create({
      collection: "contacts",
      data: {
        ...data,
        subject: data.subject || `New contact inquiry from ${data.fullName}`,
        status: "new",
        priority: "medium",
        source: "website",
      },
    });

    return contact;
  } catch (error) {
    console.error("Error creating contact:", error);
    throw error;
  }
}

// Server function to get translations
export async function getTranslationsServer(language: "pl" | "en" = "pl") {
  try {
    const payload = await getPayloadInstance();

    const { docs } = await payload.find({
      collection: "translations",
      limit: 1000,
    });

    const translations: Record<string, string> = {};
    docs.forEach((translation: Translation) => {
      const value =
        language === "pl" ? translation.polish : translation.english;
      translations[translation.key] = value;
    });

    return translations;
  } catch (error) {
    console.error("Error fetching translations:", error);
    return {};
  }
}

// Server function to get SEO data
export async function getSEOServer(slug: string, language: "pl" | "en" = "pl") {
  try {
    const payload = await getPayloadInstance();

    const { docs } = await payload.find({
      collection: "seo",
      where: {
        slug: { equals: slug },
        language: { equals: language },
      },
      limit: 1,
    });

    return docs.length > 0 ? docs[0] : null;
  } catch (error) {
    console.error("Error fetching SEO data:", error);
    return null;
  }
}

// Server function to get case studies
export async function getCaseStudiesServer(
  language: "pl" | "en" = "pl",
  limit?: number
) {
  try {
    const payload = await getPayloadInstance();

    const { docs } = await payload.find({
      collection: "case-studies",
      where: {
        status: { equals: "published" },
        language: { equals: language },
      },
      sort: "-date",
      limit: limit || 0,
    });

    return docs;
  } catch (error) {
    console.error("Error fetching case studies:", error);
    return [];
  }
}

// Server function to get single case study
export async function getCaseStudyServer(
  id: string,
  language: "pl" | "en" = "pl"
) {
  try {
    const payload = await getPayloadInstance();

    const caseStudy = await payload.findByID({
      collection: "case-studies",
      id,
      locale: language,
    });

    return caseStudy;
  } catch (error) {
    console.error("Error fetching case study:", error);
    return null;
  }
}
