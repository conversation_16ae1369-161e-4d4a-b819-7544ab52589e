import { getPayload } from "payload";
import config from "@/payload.config";
import { withCache } from "./cache";

// Import custom types (temporary solution due to Node.js version incompatibility)
import type {
  Translation,
  Seo,
  CaseStudy,
  Contact,
  Media,
} from "@/custom-payload-types";

// Re-export types for convenience
export type PayloadTranslation = Translation;
export type PayloadSEO = Seo;
export type PayloadCaseStudy = CaseStudy;
export type PayloadContact = Contact;
export type PayloadMedia = Media;

// Get Payload instance
async function getPayloadInstance() {
  return await getPayload({ config });
}

// Real Payload API functions using Local API with auto-generated types
export async function getTranslations(
  language: "pl" | "en" = "pl"
): Promise<Record<string, string>> {
  return withCache(
    `translations-${language}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API with full type safety
        const { docs } = await payload.find({
          collection: "translations",
          limit: 1000,
        });

        const translations: Record<string, string> = {};

        // Transform translations with proper typing
        docs.forEach((translation) => {
          const value =
            language === "pl" ? translation.polish : translation.english;
          translations[translation.key] = value;
        });

        return translations;
      } catch (error) {
        console.error("Error fetching translations:", error);
        return {};
      }
    },
    60 // Cache for 60 minutes
  );
}

export async function getSEOData(
  slug: string,
  language: "pl" | "en" = "pl"
): Promise<PayloadSEO | null> {
  return withCache(
    `seo-data-${slug}-${language}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API with typed queries
        const { docs } = await payload.find({
          collection: "seo",
          where: {
            slug: { equals: slug },
            language: { equals: language },
          },
          limit: 1,
        });

        return docs.length > 0 ? docs[0] : null;
      } catch (error) {
        console.error("Error fetching SEO data:", error);
        return null;
      }
    },
    30 // Cache for 30 minutes
  );
}

export async function getCaseStudies(
  language: "pl" | "en" = "pl"
): Promise<PayloadCaseStudy[]> {
  return withCache(
    `case-studies-${language}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API with typed queries
        const { docs } = await payload.find({
          collection: "case-studies",
          where: {
            language: { equals: language },
            status: { equals: "published" },
          },
          sort: "-date",
        });

        return docs;
      } catch (error) {
        console.error("Error fetching case studies:", error);
        return [];
      }
    },
    20 // Cache for 20 minutes
  );
}

export async function getCaseStudy(
  id: string,
  language: "pl" | "en" = "pl"
): Promise<PayloadCaseStudy | null> {
  return withCache(
    `case-study-${id}-${language}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API to find by ID
        const caseStudy = await payload.findByID({
          collection: "case-studies",
          id,
          locale: language,
        });

        return caseStudy;
      } catch (error) {
        console.error("Error fetching case study:", error);
        return null;
      }
    },
    30 // Cache for 30 minutes
  );
}

export async function createContact(
  contactData: Pick<
    PayloadContact,
    "fullName" | "email" | "phone" | "message" | "subject"
  >
): Promise<PayloadContact> {
  try {
    const payload = await getPayloadInstance();

    // Use Payload Local API to create contact with full type safety
    const contact = await payload.create({
      collection: "contacts",
      data: {
        ...contactData,
        status: "new",
        priority: "medium",
        source: "website",
      },
    });

    // Send notification (you can implement email service here)
    console.log("New contact submission:", {
      name: contact.fullName,
      email: contact.email,
      subject: contact.subject,
    });

    return contact;
  } catch (error) {
    console.error("Error submitting contact form:", error);
    throw error;
  }
}

// Helper function to fetch case studies for homepage (limited number)
export async function getFeaturedCaseStudies(
  language: "pl" | "en" = "pl",
  limit: number = 2
): Promise<PayloadCaseStudy[]> {
  return withCache(
    `featured-case-studies-${language}-${limit}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API with typed queries and limit
        const { docs } = await payload.find({
          collection: "case-studies",
          where: {
            status: { equals: "published" },
            language: { equals: language },
          },
          limit,
          sort: "-date",
        });

        return docs;
      } catch (error) {
        console.error("Error fetching featured case studies:", error);
        return [];
      }
    },
    10 // Cache for 10 minutes
  );
}

// Helper function to fetch all published case studies
export async function getAllCaseStudies(
  language: "pl" | "en" = "pl"
): Promise<PayloadCaseStudy[]> {
  return withCache(
    `all-case-studies-${language}`,
    async () => {
      try {
        const payload = await getPayloadInstance();

        // Use Payload Local API to get all published case studies
        const { docs } = await payload.find({
          collection: "case-studies",
          where: {
            status: { equals: "published" },
            language: { equals: language },
          },
          sort: "-date",
          limit: 0, // Get all documents
        });

        return docs;
      } catch (error) {
        console.error("Error fetching case studies:", error);
        return [];
      }
    },
    15 // Cache for 15 minutes
  );
}

// Helper function to generate structured data
export function generateStructuredData(
  type: "website" | "organization" | "article",
  data: any
) {
  const baseUrl = process.env.NEXT_PUBLIC_URL || "https://defencesystem.pl";

  const commonData = {
    "@context": "https://schema.org",
  };

  switch (type) {
    case "website":
      return {
        ...commonData,
        "@type": "WebSite",
        name: data.name || "Defence System",
        description: data.description,
        url: baseUrl,
        potentialAction: {
          "@type": "SearchAction",
          target: `${baseUrl}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string",
        },
      };

    case "organization":
      return {
        ...commonData,
        "@type": "Organization",
        name: "Defence System",
        description: "Profesjonalne systemy bezpieczeństwa Ajax Systems",
        url: baseUrl,
        logo: `${baseUrl}/placeholder-logo.png`,
        contactPoint: {
          "@type": "ContactPoint",
          telephone: "+48-XXX-XXX-XXX",
          contactType: "customer service",
          availableLanguage: ["Polish", "English"],
        },
        address: {
          "@type": "PostalAddress",
          streetAddress: "Cybernetyki 12",
          addressLocality: "Warsaw",
          postalCode: "02-677",
          addressCountry: "PL",
        },
      };

    case "article":
      return {
        ...commonData,
        "@type": "Article",
        headline: data.title,
        description: data.description,
        author: {
          "@type": "Organization",
          name: "Defence System",
        },
        publisher: {
          "@type": "Organization",
          name: "Defence System",
          logo: {
            "@type": "ImageObject",
            url: `${baseUrl}/placeholder-logo.png`,
          },
        },
        datePublished: data.datePublished,
        dateModified: data.dateModified || data.datePublished,
      };

    default:
      return commonData;
  }
}
