// Server-side Payload Local API integration
// This file provides server-side access to Payload CMS data
import config from "@/payload.config";
import { getPayload } from "payload";

let cachedPayload: any = null;

// Get Payload instance for server-side operations
export const getPayloadClient = async () => {
  if (cachedPayload) {
    return cachedPayload;
  }

  try {
    cachedPayload = await getPayload({ config });
    return cachedPayload;
  } catch (error) {
    console.error("Error initializing Payload:", error);
    throw error;
  }
};

// Server-side functions that use Payload Local API
export async function getTranslationsFromPayload(language: "pl" | "en" = "pl") {
  try {
    const payload = await getPayloadClient();
    const translations = await payload.find({
      collection: "translations",
      limit: 1000,
      where: {
        // Add any filtering if needed
      },
    });

    const translationMap: Record<string, string> = {};
    translations.docs.forEach((translation: any) => {
      const value =
        language === "pl" ? translation.polish : translation.english;
      translationMap[translation.key] = value;
    });

    return translationMap;
  } catch (error) {
    console.error("Error fetching translations from Payload:", error);
    return {};
  }
}

export async function getSEOFromPayload(
  slug: string,
  language: "pl" | "en" = "pl"
) {
  try {
    const payload = await getPayloadClient();
    const seoData = await payload.find({
      collection: "seo",
      where: {
        slug: { equals: slug },
        language: { equals: language },
      },
      limit: 1,
    });

    return seoData.docs.length > 0 ? seoData.docs[0] : null;
  } catch (error) {
    console.error("Error fetching SEO data from Payload:", error);
    return null;
  }
}

export async function getCaseStudiesFromPayload(
  language: "pl" | "en" = "pl",
  limit?: number,
  status: "draft" | "published" = "published"
) {
  try {
    const payload = await getPayloadClient();
    const caseStudies = await payload.find({
      collection: "case-studies",
      where: {
        status: { equals: status },
        language: { equals: language },
      },
      limit: limit || 0,
      sort: "-date",
    });

    return caseStudies.docs;
  } catch (error) {
    console.error("Error fetching case studies from Payload:", error);
    return [];
  }
}

export async function getCaseStudyFromPayload(id: string) {
  try {
    const payload = await getPayloadClient();
    const caseStudy = await payload.findByID({
      collection: "case-studies",
      id,
    });

    return caseStudy;
  } catch (error) {
    console.error("Error fetching case study from Payload:", error);
    return null;
  }
}

export async function createContactInPayload(contactData: any) {
  try {
    const payload = await getPayloadClient();
    const contact = await payload.create({
      collection: "contacts",
      data: {
        ...contactData,
        status: "new",
        priority: "medium",
        source: "website",
      },
    });

    // Send notification email
    await sendContactNotification(contact);

    return contact;
  } catch (error) {
    console.error("Error creating contact in Payload:", error);
    throw error;
  }
}

async function sendContactNotification(contact: any) {
  // Implement email notification logic
  // This could use Payload's email functionality or external service
  console.log("New contact submission:", {
    name: contact.fullName,
    email: contact.email,
    subject: contact.subject,
  });

  // Example email notification (implement with your preferred email service)
  /*
  await emailService.send({
    to: process.env.ADMIN_EMAIL,
    subject: `New Contact: ${contact.subject}`,
    html: `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${contact.fullName}</p>
      <p><strong>Email:</strong> ${contact.email}</p>
      <p><strong>Phone:</strong> ${contact.phone || 'Not provided'}</p>
      <p><strong>Message:</strong></p>
      <p>${contact.message}</p>
    `,
  });
  */
}
