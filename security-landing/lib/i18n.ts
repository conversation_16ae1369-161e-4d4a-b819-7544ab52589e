import i18n from "i18next"
import { initReactI18next } from "react-i18next"

const resources = {
  pl: {
    translation: {
      nav: {
        home: "Strona główna",
        contact: "Konta<PERSON>",
        howWeWork: "Jak pracujemy",
        products: "Produkty",
        caseStudies: "Realizacje",
      },
      hero: {
        title: "TWOJE BEZPIECZEŃSTWO",
        subtitle: "NASZ PRIORYTET",
        partner: "OFICJALNY PARTNER AJAX SYSTEMS",
        cta: "UMÓW KONSULTACJĘ",
      },
      howWeWork: {
        title: "JAK PRACUJEMY",
        subtitle: "Kompleksowe podejście — dobieramy, instalujemy i ustawiamy wszystko pod Twój obiekt",
        sales: "SPRZEDAŻ",
        installation: "MONTAŻ",
        programming: "PROGRAMOWANIE",
        steps: {
          contact: "KONTAKT",
          consultation: "KONSULTACJA I PRZYJAZD",
          selection: "DOBÓR SYSTEMU",
          installation: "MON<PERSON><PERSON>",
          configuration: "KONFIGURACJA",
          support: "WSPARCIE",
        },
      },
      products: {
        title: "SYSTEMY BEZPIECZEŃSTWA",
        subtitle: "Profesjonalne komponenty Ajax Systems dla kompleksowej ochrony",
      },
      caseStudies: {
        title: "REALIZACJE",
        subtitle: "Prawdziwe projekty, prawdziwe rezultaty — zobacz jak chronimy to, co najważniejsze",
        viewAll: "ZOBACZ WSZYSTKIE REALIZACJE",
        categories: {
          residential: "Mieszkalnictwo",
          commercial: "Komercyjne",
        },
        projects: {
          villa: {
            title: "Ochrona Nowoczesnej Willi",
            description:
              "Kompletna instalacja systemu bezpieczeństwa dla willi o powierzchni 500m² obejmująca ochronę obwodową, monitoring wideo i integrację z inteligentnym domem.",
          },
          office: {
            title: "Bezpieczeństwo Kompleksu Biurowego",
            description:
              "Wielostrefowy system bezpieczeństwa dla 3-piętrowego budynku biurowego z kontrolą dostępu, ochroną przeciwpożarową i integracją z monitoringiem 24/7.",
          },
        },
      },
      forWhom: {
        title: "Dla kogo?",
        subtitle: "Nasze systemy montujemy wszędzie tam, gdzie liczy się bezpieczeństwo:",
        homes: "Domy i mieszkania – pełna ochrona rodziny i majątku.",
        offices: "Biura i firmy – kontrola dostępu, monitoring i bezpieczeństwo pracowników.",
        shops: "Sklepy, salony, restauracje – ochrona towaru i całodobowy podgląd.",
        developments: "Inwestycje deweloperskie – kompleksowe systemy dla budynków i osiedli.",
        areas: "Tereny i parkingi – kamery, analiza obrazu (AI), ochrona obiektów zewnętrznych.",
      },
      protection: {
        antiburglary: {
          title: "Ochrona antywłamaniowa",
          motion: "czujniki ruchu",
          doors: "czujniki otwarcia drzwi i okien",
          tilt: "czujniki pochylenia okien",
          glass: "czujniki zbicia szyby",
        },
        fire: {
          title: "Ochrona przeciwpożarowa",
          smoke: "czujniki dymu",
          temperature: "czujniki temperatury",
          sirens: "syreny wewnętrzne i zewnętrzne",
        },
        flood: {
          title: "Ochrona przeciwzalaniowa",
          leak: "czujniki wycieku wody",
          valve: "automatyczny zawór odcinający wodę",
        },
        video: {
          title: "Monitoring wideo",
          cameras: "kamery IP z podglądem na żywo w aplikacji",
          ai: "sztuczna inteligencja: rozpoznawanie osoby, pojazdu, zwierzęcia",
          recording: "zapis i archiwizacja nagrań",
        },
        integration: {
          title: "Integracja i powiadomienia",
          push: "powiadomienia push 24/7",
          remote: "zdalny dostęp przez aplikację",
          security: "podłączenie do agencji ochrony",
        },
      },
      cta: {
        title: "Umów się na bezpłatną konsultację – przyjedziemy i doradzimy.",
        button: "Skontaktuj się z nami",
      },
      contact: {
        title: "Skontaktuj się z nami",
        name: "Imię i nazwisko",
        email: "Email",
        phone: "Telefon",
        message: "Wiadomość",
        submit: "Wyślij wiadomość",
        office: {
          title: "Nasze Biuro",
          address: "Cybernetyki 12, 02-677 Warszawa",
          mapTitle: "Lokalizacja biura Defence System",
        },
      },
    },
  },
  en: {
    translation: {
      nav: {
        home: "Home",
        contact: "Contact",
        howWeWork: "How we work",
        products: "Products",
        caseStudies: "Case studies",
      },
      hero: {
        title: "YOUR SAFETY",
        subtitle: "OUR PRIORITY",
        partner: "OFFICIAL PARTNER AJAX SYSTEMS",
        cta: "GET CONSULTATION",
      },
      howWeWork: {
        title: "HOW WE WORK",
        subtitle: "Comprehensive approach — we select, install and configure everything for your facility",
        sales: "SALES",
        installation: "INSTALLATION",
        programming: "PROGRAMMING",
        steps: {
          contact: "CONTACT",
          consultation: "CONSULTATION & VISIT",
          selection: "SYSTEM SELECTION",
          installation: "INSTALLATION",
          configuration: "CONFIGURATION",
          support: "ONGOING SUPPORT",
        },
      },
      products: {
        title: "SECURITY SYSTEMS",
        subtitle: "Professional Ajax Systems components for comprehensive protection",
      },
      caseStudies: {
        title: "CASE STUDIES",
        subtitle: "Real projects, real results — see how we protect what matters most",
        viewAll: "VIEW ALL CASE STUDIES",
        categories: {
          residential: "Residential",
          commercial: "Commercial",
        },
        projects: {
          villa: {
            title: "Modern Villa Protection",
            description:
              "Complete security system installation for a 500m² villa including perimeter protection, video surveillance, and smart home integration.",
          },
          office: {
            title: "Office Complex Security",
            description:
              "Multi-zone security system for a 3-story office building with access control, fire protection, and 24/7 monitoring integration.",
          },
        },
      },
      forWhom: {
        title: "For whom?",
        subtitle: "We install our systems everywhere security matters:",
        homes: "Homes and apartments – complete protection of family and property.",
        offices: "Offices and companies – access control, monitoring and employee security.",
        shops: "Shops, salons, restaurants – goods protection and 24/7 monitoring.",
        developments: "Development investments – comprehensive systems for buildings and housing estates.",
        areas: "Areas and parking lots – cameras, image analysis (AI), protection of external objects.",
      },
      protection: {
        antiburglary: {
          title: "Anti-burglary protection",
          motion: "motion sensors",
          doors: "door and window opening sensors",
          tilt: "window tilt sensors",
          glass: "glass break sensors",
        },
        fire: {
          title: "Fire protection",
          smoke: "smoke detectors",
          temperature: "temperature sensors",
          sirens: "internal and external sirens",
        },
        flood: {
          title: "Flood protection",
          leak: "water leak sensors",
          valve: "automatic water shut-off valve",
        },
        video: {
          title: "Video monitoring",
          cameras: "IP cameras with live view in the app",
          ai: "artificial intelligence: person, vehicle, animal recognition",
          recording: "recording and archiving of footage",
        },
        integration: {
          title: "Integration and notifications",
          push: "push notifications 24/7",
          remote: "remote access via app",
          security: "connection to security agency",
        },
      },
      cta: {
        title: "Schedule a free consultation – we'll come and advise.",
        button: "Contact us",
      },
      contact: {
        title: "Contact us",
        name: "Full name",
        email: "Email",
        phone: "Phone",
        message: "Message",
        submit: "Send message",
        office: {
          title: "Our Office",
          address: "Cybernetyki 12, 02-677 Warsaw",
          mapTitle: "Defence System office location",
        },
      },
    },
  },
}

i18n.use(initReactI18next).init({
  resources,
  lng: "pl",
  fallbackLng: "pl",
  interpolation: {
    escapeValue: false,
  },
})

export default i18n
