# Defence System - Security Landing Page

A production-ready Next.js website for a security systems company with PayloadCMS integration, multilingual support, and comprehensive SEO optimization.

## 🚀 Features

### Content Management

- **PayloadCMS Integration**: Full CMS for managing content, translations, case studies, and contact submissions
- **Dynamic Content**: All text and content pulled from Payload collections
- **Multilingual Support**: Polish and English translations managed through CMS
- **Case Studies**: Dynamic case studies with rich content and images

### SEO & Performance

- **Advanced SEO**: Dynamic meta tags, Open Graph, Twitter Cards, structured data
- **Sitemap & Robots**: Automatically generated for search engines
- **Performance Optimized**: Caching, image optimization, bundle optimization
- **Core Web Vitals**: Optimized for Google's performance metrics

### User Experience

- **Responsive Design**: Mobile-first approach with beautiful UI
- **Error Boundaries**: Graceful error handling and recovery
- **Loading States**: Smooth loading experiences
- **Contact Form**: Integrated with Payload for lead management

### Production Ready

- **Security Headers**: Comprehensive security configuration
- **Analytics**: Google Analytics and Vercel Analytics integration
- **Monitoring**: Error tracking and performance monitoring
- **Caching**: Intelligent caching for optimal performance

## 📁 Project Structure

```
/
├── app/                     # Next.js app directory
│   ├── (app)/              # Main website pages
│   ├── (payload)/          # Payload admin interface
│   ├── sitemap.ts          # Dynamic sitemap generation
│   └── robots.ts           # SEO robots configuration
├── collections/            # Payload CMS collections
│   ├── Translations.ts     # Multilingual content
│   ├── SEO.ts             # SEO metadata
│   ├── Pages.ts           # Page content
│   ├── CaseStudies.ts     # Project showcases
│   ├── Contacts.ts        # Contact form submissions
│   └── Users.ts           # Admin users
├── components/            # Reusable React components
│   ├── ui/               # UI component library
│   ├── shared-layout.tsx # Common layout wrapper
│   ├── case-studies-section.tsx
│   ├── contact-form.tsx
│   └── analytics.tsx
├── lib/                  # Utility functions
│   ├── payload.ts       # Payload API integration
│   ├── cache.ts         # Performance caching
│   ├── error-boundary.tsx
│   └── i18n.ts          # Internationalization
└── payload.config.ts    # Payload CMS configuration
```

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **CMS**: PayloadCMS 3.x
- **Database**: SQLite (configurable to PostgreSQL/MongoDB)
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI + custom components
- **Internationalization**: react-i18next with Payload integration
- **Analytics**: Vercel Analytics + Google Analytics
- **Deployment**: Optimized for Vercel/any hosting platform

## 🔧 Setup & Installation

### Prerequisites

- Node.js 18+ and pnpm
- Basic understanding of Next.js and PayloadCMS

### Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
# Payload CMS
PAYLOAD_SECRET=your-super-secret-key
DATABASE_URI=file:./payload.db

# Public URLs
NEXT_PUBLIC_URL=https://your-domain.com
NEXT_PUBLIC_PAYLOAD_URL=https://your-domain.com

# Analytics (optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
VERCEL_ANALYTICS_ID=your-vercel-id

# Email (for contact notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Installation

```bash
# Install dependencies
pnpm install

# Set up database and admin user
pnpm dev
# Visit http://localhost:3000/admin to create first admin user

# Build for production
pnpm build
pnpm start
```

## 📊 CMS Collections Overview

### Translations

Manage all website text in multiple languages:

- Navigation items
- Hero sections
- Product descriptions
- UI labels

### SEO

Complete SEO management per page:

- Meta titles and descriptions
- Open Graph images
- Keywords and canonical URLs
- Structured data (JSON-LD)

### Case Studies

Rich project showcases:

- Project details (location, date, client)
- Challenge, solution, and results
- Featured and additional images
- Multilingual content

### Contacts

Lead management system:

- Contact form submissions
- Status tracking (new, in progress, resolved)
- Priority assignment
- Internal notes and follow-ups

## 🎨 Design System

The website uses a sophisticated design system with:

- **Typography**: Light, elegant fonts with proper hierarchy
- **Colors**: Professional palette with slate grays and accent colors
- **Layout**: Spacious, modern layouts with subtle animations
- **Components**: Consistent, reusable UI components
- **Responsive**: Mobile-first design that works on all devices

## 📈 Performance Features

### Caching Strategy

- **In-memory caching**: For frequently accessed data
- **API response caching**: Case studies and translations
- **Image optimization**: Next.js automatic optimization
- **Bundle optimization**: Tree shaking and code splitting

### SEO Optimization

- **Dynamic meta tags**: Generated from Payload data
- **Structured data**: Rich snippets for search engines
- **Sitemap**: Automatically updated with new content
- **Core Web Vitals**: Optimized loading and interactivity

## 🔒 Security Features

- **Security headers**: XSS protection, frame options, content security
- **Input validation**: All forms properly validated
- **Error boundaries**: Graceful error handling
- **Rate limiting**: Protection against spam and abuse

## 🌐 Multilingual Support

Complete internationalization with:

- **Dynamic translations**: All text managed in Payload
- **Language switching**: Seamless language changing
- **SEO for multiple languages**: Proper hreflang implementation
- **Content localization**: Different content per language

## 📱 Responsive Design

Mobile-first approach with:

- **Breakpoint system**: Consistent responsive behavior
- **Touch-friendly**: Optimized for mobile interactions
- **Progressive enhancement**: Works without JavaScript
- **Fast loading**: Optimized for mobile networks

## 🚀 Deployment

### Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Other Platforms

The app is platform-agnostic and can be deployed on:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS/GCP/Azure

## 📞 Support & Customization

This is a complete, production-ready website that can be easily customized for different businesses. The modular architecture allows for easy feature additions and modifications.

Key customization points:

- **Branding**: Colors, fonts, and logos in the design system
- **Content**: All managed through Payload CMS
- **Features**: Modular components for easy additions
- **Integrations**: APIs and third-party services

## 📄 License

Private project - All rights reserved.

---

Built with ❤️ for modern web experiences.
