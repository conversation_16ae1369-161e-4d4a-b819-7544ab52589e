"use client";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { getTranslationsServer } from "@/lib/payload-server";

export function usePayloadTranslations() {
  const { i18n } = useTranslation();
  const [payloadTranslations, setPayloadTranslations] = useState<
    Record<string, string>
  >({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTranslations = async () => {
      try {
        const translations = await getTranslationsServer(
          i18n.language as "pl" | "en"
        );
        setPayloadTranslations(translations);

        // Update i18next store with payload translations
        if (Object.keys(translations).length > 0) {
          i18n.addResourceBundle(
            i18n.language,
            "translation",
            translations,
            true,
            true
          );
        }
      } catch (error) {
        console.error("Error fetching Payload translations:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTranslations();
  }, [i18n.language, i18n]);

  return { translations: payloadTranslations, loading };
}
