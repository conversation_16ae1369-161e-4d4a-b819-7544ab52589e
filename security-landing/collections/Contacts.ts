import type { CollectionConfig } from "payload";

export const Contacts: CollectionConfig = {
  slug: "contacts",
  admin: {
    useAsTitle: "fullName",
    defaultColumns: ["fullName", "email", "phone", "createdAt"],
    group: "Communications",
  },
  access: {
    read: () => true,
    create: () => true, // Allow public creation for contact form
  },
  fields: [
    {
      name: "fullName",
      type: "text",
      required: true,
      label: "Full Name",
    },
    {
      name: "email",
      type: "email",
      required: true,
      label: "Email Address",
    },
    {
      name: "phone",
      type: "text",
      label: "Phone Number",
    },
    {
      name: "message",
      type: "textarea",
      required: true,
      label: "Message",
    },
    {
      name: "subject",
      type: "text",
      label: "Subject",
      admin: {
        description: "Automatically generated or provided by user",
      },
    },
    {
      name: "status",
      type: "select",
      options: [
        { label: "New", value: "new" },
        { label: "In Progress", value: "in_progress" },
        { label: "Resolved", value: "resolved" },
        { label: "Closed", value: "closed" },
      ],
      defaultValue: "new",
      required: true,
      label: "Status",
    },
    {
      name: "priority",
      type: "select",
      options: [
        { label: "Low", value: "low" },
        { label: "Medium", value: "medium" },
        { label: "High", value: "high" },
        { label: "Urgent", value: "urgent" },
      ],
      defaultValue: "medium",
      label: "Priority",
    },
    {
      name: "source",
      type: "select",
      options: [
        { label: "Website Contact Form", value: "website" },
        { label: "Phone Call", value: "phone" },
        { label: "Email", value: "email" },
        { label: "Social Media", value: "social" },
        { label: "Referral", value: "referral" },
      ],
      defaultValue: "website",
      label: "Source",
    },
    {
      name: "notes",
      type: "richText",
      label: "Internal Notes",
      admin: {
        description: "Internal notes for staff use only",
      },
    },
    {
      name: "followUpDate",
      type: "date",
      label: "Follow-up Date",
    },
    {
      name: "assignedTo",
      type: "relationship",
      relationTo: "users",
      label: "Assigned To",
      admin: {
        description: "Staff member assigned to handle this contact",
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Auto-generate subject if not provided
        if (operation === "create" && !data.subject) {
          data.subject = `New contact inquiry from ${data.fullName}`;
        }
        return data;
      },
    ],
    afterChange: [
      ({ doc, operation }) => {
        // Here you could add email notifications, webhooks, etc.
        if (operation === "create") {
          console.log(`New contact submission from ${doc.fullName}`);
          // TODO: Send notification email to admin
        }
      },
    ],
  },
};
