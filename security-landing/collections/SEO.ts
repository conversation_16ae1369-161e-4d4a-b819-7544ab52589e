import type { CollectionConfig } from 'payload'

export const SEO: CollectionConfig = {
  slug: 'seo',
  admin: {
    useAsTitle: 'pageName',
    defaultColumns: ['pageName', 'language', 'metaTitle'],
    group: 'Content Management',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'pageName',
      type: 'text',
      required: true,
      label: 'Page Name',
      admin: {
        description: 'Internal name for this page (e.g., "Home Page", "Case Studies")',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'Page Slug',
      admin: {
        description: 'URL path for this page (e.g., "/", "/case-studies")',
      },
    },
    {
      name: 'language',
      type: 'select',
      options: [
        { label: 'Polish', value: 'pl' },
        { label: 'English', value: 'en' },
      ],
      required: true,
      label: 'Language',
    },
    {
      name: 'metaTitle',
      type: 'text',
      required: true,
      label: 'Meta Title',
      admin: {
        description: 'Title that appears in browser tabs and search results (recommended: 50-60 characters)',
      },
    },
    {
      name: 'metaDescription',
      type: 'textarea',
      required: true,
      label: 'Meta Description',
      admin: {
        description: 'Description that appears in search results (recommended: 150-160 characters)',
      },
    },
    {
      name: 'keywords',
      type: 'text',
      label: 'Keywords',
      admin: {
        description: 'Comma-separated keywords for SEO (e.g., "security systems, Ajax, surveillance")',
      },
    },
    {
      name: 'ogImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Open Graph Image',
      admin: {
        description: 'Image for social media sharing (recommended: 1200x630px)',
      },
    },
    {
      name: 'ogTitle',
      type: 'text',
      label: 'Open Graph Title',
      admin: {
        description: 'Title for social media sharing (if different from meta title)',
      },
    },
    {
      name: 'ogDescription',
      type: 'textarea',
      label: 'Open Graph Description',
      admin: {
        description: 'Description for social media sharing (if different from meta description)',
      },
    },
    {
      name: 'canonicalUrl',
      type: 'text',
      label: 'Canonical URL',
      admin: {
        description: 'Preferred URL for this page to avoid duplicate content issues',
      },
    },
    {
      name: 'noIndex',
      type: 'checkbox',
      label: 'No Index',
      admin: {
        description: 'Prevent search engines from indexing this page',
      },
    },
    {
      name: 'structuredData',
      type: 'json',
      label: 'Structured Data (JSON-LD)',
      admin: {
        description: 'JSON-LD structured data for rich snippets',
      },
    },
  ],
}
