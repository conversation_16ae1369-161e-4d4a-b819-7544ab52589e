import type { CollectionConfig } from 'payload'

export const Pages: CollectionConfig = {
  slug: 'pages',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', 'language', 'status'],
    group: 'Content Management',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Page Title',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      label: 'URL Slug',
      admin: {
        description: 'URL path for this page (e.g., "/", "/case-studies")',
      },
    },
    {
      name: 'language',
      type: 'select',
      options: [
        { label: 'Polish', value: 'pl' },
        { label: 'English', value: 'en' },
      ],
      required: true,
      label: 'Language',
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'content',
      type: 'richText',
      label: 'Page Content',
    },
    {
      name: 'seo',
      type: 'relationship',
      relationTo: 'seo',
      label: 'SEO Settings',
      admin: {
        description: 'Link to SEO configuration for this page',
      },
    },
  ],
}
