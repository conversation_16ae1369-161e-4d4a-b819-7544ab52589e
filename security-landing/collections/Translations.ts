import type { CollectionConfig } from 'payload'

export const Translations: CollectionConfig = {
  slug: 'translations',
  admin: {
    useAsTitle: 'key',
    defaultColumns: ['key', 'polish', 'english'],
    group: 'Content Management',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'key',
      type: 'text',
      required: true,
      unique: true,
      label: 'Translation Key',
      admin: {
        description: 'Unique identifier for this translation (e.g., hero.title, nav.home)',
      },
    },
    {
      name: 'polish',
      type: 'text',
      required: true,
      label: 'Polish Translation',
    },
    {
      name: 'english',
      type: 'text',
      required: true,
      label: 'English Translation',
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'Navigation', value: 'navigation' },
        { label: 'Hero Section', value: 'hero' },
        { label: 'How We Work', value: 'howWeWork' },
        { label: 'Products', value: 'products' },
        { label: 'Case Studies', value: 'caseStudies' },
        { label: 'Protection Types', value: 'protection' },
        { label: 'Contact', value: 'contact' },
        { label: 'General', value: 'general' },
      ],
      label: 'Category',
      admin: {
        description: 'Categorize translations for better organization',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Optional description of what this translation is used for',
      },
    },
  ],
}
