import type { CollectionConfig } from 'payload'

export const CaseStudies: CollectionConfig = {
  slug: 'case-studies',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'language', 'date'],
    group: 'Content Management',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Project Title',
    },
    {
      name: 'language',
      type: 'select',
      options: [
        { label: 'Polish', value: 'pl' },
        { label: 'English', value: 'en' },
      ],
      required: true,
      label: 'Language',
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'Residential', value: 'residential' },
        { label: 'Commercial', value: 'commercial' },
        { label: 'Retail', value: 'retail' },
        { label: 'Industrial', value: 'industrial' },
      ],
      required: true,
      label: 'Category',
    },
    {
      name: 'location',
      type: 'text',
      required: true,
      label: 'Location',
    },
    {
      name: 'date',
      type: 'date',
      required: true,
      label: 'Project Date',
    },
    {
      name: 'client',
      type: 'text',
      required: true,
      label: 'Client Type',
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: 'Featured Image',
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      label: 'Project Description',
    },
    {
      name: 'challenge',
      type: 'textarea',
      required: true,
      label: 'Challenge',
    },
    {
      name: 'solution',
      type: 'textarea',
      required: true,
      label: 'Solution',
    },
    {
      name: 'results',
      type: 'array',
      label: 'Results',
      fields: [
        {
          name: 'result',
          type: 'text',
          required: true,
          label: 'Result Item',
        },
      ],
    },
    {
      name: 'additionalImages',
      type: 'array',
      label: 'Additional Images',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
          label: 'Image Caption',
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
      ],
      defaultValue: 'draft',
      required: true,
    },
  ],
}
