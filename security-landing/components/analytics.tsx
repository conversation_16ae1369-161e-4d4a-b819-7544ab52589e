"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";

// Google Analytics
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  const pathname = usePathname();

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Load Google Analytics script
    const script = document.createElement("script");
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script.async = true;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    gtag("js", new Date());
    gtag("config", gaId);

    // Attach gtag to window for global access
    (window as any).gtag = gtag;

    return () => {
      document.head.removeChild(script);
    };
  }, [gaId]);

  useEffect(() => {
    if (typeof window !== "undefined" && (window as any).gtag) {
      (window as any).gtag("config", gaId, {
        page_path: pathname,
      });
    }
  }, [pathname, gaId]);

  return null;
}

// Event tracking utilities
export const trackEvent = (
  eventName: string,
  parameters?: Record<string, any>
) => {
  if (typeof window !== "undefined" && (window as any).gtag) {
    (window as any).gtag("event", eventName, parameters);
  }
};

export const trackPageView = (page_path: string) => {
  if (typeof window !== "undefined" && (window as any).gtag) {
    (window as any).gtag("config", process.env.NEXT_PUBLIC_GA_ID, {
      page_path,
    });
  }
};

// Custom hook for tracking events
export function useAnalytics() {
  return {
    trackEvent,
    trackPageView,
    trackContactFormSubmission: () => trackEvent("contact_form_submit"),
    trackCaseStudyView: (caseStudyId: string, title: string) =>
      trackEvent("case_study_view", {
        case_study_id: caseStudyId,
        case_study_title: title,
      }),
    trackLanguageChange: (language: string) =>
      trackEvent("language_change", { language }),
    trackNavigationClick: (section: string) =>
      trackEvent("navigation_click", { section }),
  };
}

// Declare global types
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}
