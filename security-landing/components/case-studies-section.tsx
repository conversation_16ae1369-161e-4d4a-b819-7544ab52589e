"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { getFeaturedCaseStudies, type PayloadCaseStudy } from "@/lib/payload";
import { usePayloadTranslations } from "@/hooks/use-payload-translations";

export function CaseStudiesSection() {
  const { t, i18n } = useTranslation();
  const { loading: translationsLoading } = usePayloadTranslations();
  const [caseStudies, setCaseStudies] = useState<PayloadCaseStudy[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        const studies = await getFeaturedCaseStudies(
          i18n.language as "pl" | "en",
          2
        );
        setCaseStudies(studies);
      } catch (error) {
        console.error("Error fetching case studies:", error);
        // Fallback to static data if Payload is not available
        setCaseStudies([
          {
            id: "1",
            title: t("caseStudies.projects.villa.title"),
            category: t("caseStudies.categories.residential"),
            description: t("caseStudies.projects.villa.description"),
            featuredImage: {
              url: "/modern-villa-with-security-system-installation-ele.jpg",
            },
            language: i18n.language as "pl" | "en",
            location: "Warsaw, Poland",
            date: "2024-03-01",
            client: "Private Residence",
            challenge: "Sample challenge",
            solution: "Sample solution",
            results: [{ result: "Sample result" }],
            status: "published" as const,
          },
          {
            id: "2",
            title: t("caseStudies.projects.office.title"),
            category: t("caseStudies.categories.commercial"),
            description: t("caseStudies.projects.office.description"),
            featuredImage: {
              url: "/modern-office-building-with-security-cameras-and-a.jpg",
            },
            language: i18n.language as "pl" | "en",
            location: "Krakow, Poland",
            date: "2024-01-01",
            client: "Tech Company HQ",
            challenge: "Sample challenge",
            solution: "Sample solution",
            results: [{ result: "Sample result" }],
            status: "published" as const,
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, [i18n.language, t]);

  if (loading || translationsLoading) {
    return (
      <section
        id="case-studies"
        className="py-32 bg-gradient-to-br from-slate-50/50 to-blue-50/20"
      >
        <div className="container mx-auto px-8">
          <div className="text-center py-20">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent"></div>
            <p className="mt-4 text-slate-600 font-light">{t("loading")}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      id="case-studies"
      className="py-32 bg-gradient-to-br from-slate-50/50 to-blue-50/20"
    >
      <div className="container mx-auto px-8">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide">
            {t("caseStudies.title")}
          </h2>
          <div className="w-16 h-px bg-gradient-to-r from-transparent via-slate-400 to-transparent mx-auto mb-8"></div>
          <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed">
            {t("caseStudies.subtitle")}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto mb-16">
          {caseStudies.map((study, index) => (
            <div key={study.id || index} className="group">
              <div className="aspect-[3/2] mb-6 overflow-hidden bg-gradient-to-br from-slate-100 to-blue-100/30">
                <img
                  src={study.featuredImage?.url || "/placeholder.svg"}
                  alt={study.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                />
              </div>
              <div className="space-y-3">
                <div className="text-xs font-light tracking-wider uppercase text-slate-500">
                  {study.category}
                </div>
                <h3 className="text-2xl font-light text-slate-800 tracking-wide">
                  {study.title}
                </h3>
                <p className="text-slate-600 font-light leading-relaxed">
                  {study.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link href="/case-studies">
            <Button
              variant="outline"
              className="bg-transparent border-slate-300 text-slate-700 hover:bg-slate-50 hover:border-slate-400 transition-all duration-300 text-sm font-light tracking-wider px-8 py-3 rounded-none group"
            >
              {t("caseStudies.viewAll")}
              <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
