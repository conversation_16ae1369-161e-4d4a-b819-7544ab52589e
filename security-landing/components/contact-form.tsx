"use client";

import type React from "react";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Shield,
  Mail,
  Phone,
  User,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  XCircle,
} from "lucide-react";
import { createContactFromObject } from "@/lib/payload-server";

interface FormData {
  fullName: string;
  email: string;
  phone: string;
  message: string;
}

type SubmissionStatus = "idle" | "loading" | "success" | "error";

export function ContactForm() {
  const { t, i18n } = useTranslation();
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    email: "",
    phone: "",
    message: "",
  });
  const [status, setStatus] = useState<SubmissionStatus>("idle");
  const [error, setError] = useState<string | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus("loading");
    setError(null);

    try {
      const newContact = await createContactFromObject({
        ...formData,
        status: "new" as "new" | "in-progress" | "resolved" | "closed",
        priority: "medium" as "low" | "medium" | "high",
        source: "website-form",
        language: i18n.language as "pl" | "en",
      });

      if (newContact) {
        setStatus("success");
        setFormData({
          fullName: "",
          email: "",
          phone: "",
          message: "",
        });
      } else {
        throw new Error("Failed to create contact");
      }
    } catch (err) {
      console.error("Submission error:", err);
      setStatus("error");
      setError(t("contact.form.submitError"));
    }
  };

  return (
    <div className="bg-card p-8 shadow-lg max-w-4xl mx-auto border border-border">
      <form onSubmit={handleSubmit} className="grid md:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Input
            name="fullName"
            placeholder={t("contact.form.fullNamePlaceholder")}
            value={formData.fullName}
            onChange={handleChange}
            required
            className="bg-input text-foreground border-border focus:ring-ring focus:ring-2"
          />
          <Input
            name="email"
            type="email"
            placeholder={t("contact.form.emailPlaceholder")}
            value={formData.email}
            onChange={handleChange}
            required
            className="bg-input text-foreground border-border focus:ring-ring focus:ring-2"
          />
          <Input
            name="phone"
            type="tel"
            placeholder={t("contact.form.phonePlaceholder")}
            value={formData.phone}
            onChange={handleChange}
            className="bg-input text-foreground border-border focus:ring-ring focus:ring-2"
          />
        </div>
        <div className="space-y-6">
          <Textarea
            name="message"
            placeholder={t("contact.form.messagePlaceholder")}
            value={formData.message}
            onChange={handleChange}
            rows={7}
            required
            className="bg-input text-foreground border-border focus:ring-ring focus:ring-2"
          />
          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary-foreground text-primary-foreground hover:text-primary transition-all duration-300 text-base font-light tracking-wider px-6 py-3 group"
            disabled={status === "loading"}
          >
            {status === "loading"
              ? t("contact.form.sending")
              : t("contact.form.submit")}
          </Button>

          {status === "success" && (
            <div className="mt-4 flex items-center text-success-foreground">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              <span>{t("contact.form.successMessage")}</span>
            </div>
          )}

          {status === "error" && (
            <div className="mt-4 flex items-center text-destructive-foreground">
              <XCircle className="h-5 w-5 mr-2 text-destructive" />
              <span>{error}</span>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
