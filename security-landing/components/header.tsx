"use client";

import { Shield } from "lucide-react";
import { LanguageSwitcher } from "./language-switcher";
import { useTranslation } from "react-i18next";
import Link from "next/link";

interface HeaderProps {
  isTransparent?: boolean;
  currentPage?: string;
}

export function Header({
  isTransparent = false,
  currentPage = "home",
}: HeaderProps) {
  const { t } = useTranslation();

  const baseClasses = "w-full backdrop-blur-md relative z-50";
  const transparentClasses = "absolute top-0 left-0 right-0";
  const solidClasses = "bg-slate-900/95 border-b border-white/10";

  return (
    <header
      className={`${baseClasses} ${
        isTransparent ? transparentClasses : solidClasses
      }`}
    >
      <div className="container mx-auto px-8 py-6 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-3 group">
          <Shield className="h-7 w-7 text-white group-hover:text-white/80 transition-colors" />
          <span className="text-lg font-light text-white tracking-wide group-hover:text-white/80 transition-colors">
            DEFENCE system
          </span>
        </Link>

        <nav className="hidden md:flex items-center space-x-8">
          {currentPage === "home" ? (
            <>
              <a
                href="#about"
                className="text-white hover:text-orange-300 transition-colors text-sm font-light tracking-wide"
              >
                Про нас
              </a>
              <a
                href="#how-we-work"
                className="text-white hover:text-orange-300 transition-colors text-sm font-light tracking-wide"
              >
                Як працюємо
              </a>
              <a
                href="#security-systems"
                className="text-white hover:text-orange-300 transition-colors text-sm font-light tracking-wide"
              >
                Системи охорони
              </a>
              <Link
                href="/portfolio"
                className="text-white hover:text-orange-300 transition-colors text-sm font-light tracking-wide"
              >
                Портфоліо
              </Link>
              <a
                href="#contact"
                className="text-white hover:text-orange-300 transition-colors text-sm font-light tracking-wide"
              >
                Контакт
              </a>
            </>
          ) : (
            <>
              <Link
                href="/"
                className="text-white/90 hover:text-white transition-colors text-sm font-light tracking-wide"
              >
                Головна
              </Link>
              <span className="text-white text-sm font-light tracking-wide">
                {currentPage === "portfolio"
                  ? "Портфоліо"
                  : currentPage.toUpperCase()}
              </span>
            </>
          )}
          <LanguageSwitcher />
        </nav>
      </div>
    </header>
  );
}
