"use client"

import { <PERSON><PERSON> } from './header'
import { Shield } from 'lucide-react'
import Link from 'next/link'

interface SharedLayoutProps {
  children: React.ReactNode
  isTransparent?: boolean
  currentPage?: string
  showFooter?: boolean
}

export function SharedLayout({ 
  children, 
  isTransparent = false, 
  currentPage = "home",
  showFooter = true 
}: SharedLayoutProps) {
  return (
    <div className="min-h-screen">
      <Header isTransparent={isTransparent} currentPage={currentPage} />
      
      {children}

      {showFooter && (
        <footer className="bg-slate-800 text-white py-20">
          <div className="container mx-auto px-8">
            <div className="text-center mb-8">
              <Link
                href="/"
                className="inline-flex items-center justify-center space-x-3 mb-6 group"
              >
                <Shield className="h-6 w-6 text-white/80 group-hover:text-white transition-colors" />
                <span className="text-lg font-light tracking-wide group-hover:text-white/80 transition-colors">
                  DEFENCE system
                </span>
              </Link>
              <div className="w-12 h-px bg-white/20 mx-auto mb-6"></div>
              <p className="text-slate-400 font-light text-sm tracking-wide mb-8">
                Official partner Ajax Systems
              </p>
            </div>
            <div className="text-center pt-8 border-t border-slate-700">
              <p className="text-slate-500 text-xs font-light tracking-wide">
                © 2024 Defence System. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      )}
    </div>
  )
}
