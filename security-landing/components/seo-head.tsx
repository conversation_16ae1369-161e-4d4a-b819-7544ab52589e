"use client";

import Head from "next/head";
import type { PayloadSEO } from "@/lib/payload-client-types";

interface SEOHeadProps {
  seo?: PayloadSEO | null;
  defaultTitle?: string;
  defaultDescription?: string;
}

export function SEOHead({
  seo,
  defaultTitle = "Defence System - Twoje <PERSON>, Nasz Priorytet",
  defaultDescription = "Profesjonalne systemy bezpieczeństwa Ajax Systems. Ochrona antywłamaniowa, przeciwpożarowa, monitoring wideo. Oficjalny partner Ajax Systems w Polsce.",
}: SEOHeadProps) {
  const title = seo?.metaTitle || defaultTitle;
  const description = seo?.metaDescription || defaultDescription;
  const keywords = seo?.keywords;
  const ogImage = seo?.ogImage?.url || "/placeholder-logo.png";
  const ogTitle = seo?.ogTitle || title;
  const ogDescription = seo?.ogDescription || description;
  const canonicalUrl = seo?.canonicalUrl;
  const noIndex = seo?.noIndex;
  const structuredData = seo?.structuredData;

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}

      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={ogTitle} />
      <meta name="twitter:description" content={ogDescription} />
      <meta name="twitter:image" content={ogImage} />

      {/* SEO Directives */}
      {noIndex && <meta name="robots" content="noindex,nofollow" />}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}

      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}

      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="theme-color" content="#1e293b" />
      <link rel="icon" href="/favicon.ico" />
    </Head>
  );
}
