"use client";

import { useEffect } from "react";
import type { PayloadSEO } from "@/lib/payload-client-types";

interface SEOProviderProps {
  seoData?: PayloadSEO | null;
  children: React.ReactNode;
}

export function SEOProvider({ seoData, children }: SEOProviderProps) {
  useEffect(() => {
    if (seoData?.structuredData) {
      // Add structured data to the page
      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.text = JSON.stringify(seoData.structuredData);
      script.id = "structured-data";

      // Remove existing structured data script if present
      const existingScript = document.getElementById("structured-data");
      if (existingScript) {
        existingScript.remove();
      }

      document.head.appendChild(script);

      return () => {
        const scriptToRemove = document.getElementById("structured-data");
        if (scriptToRemove) {
          scriptToRemove.remove();
        }
      };
    }
  }, [seoData]);

  return <>{children}</>;
}
