"use client";

import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ContactForm } from "@/components/contact-form";
import { Header } from "@/components/header";
import { CaseStudiesSection } from "@/components/case-studies-section";
import { usePayloadTranslations } from "@/hooks/use-payload-translations";
import {
  ShieldCheck,
  Zap,
  Home,
  Building,
  Factory,
  Video,
  Lightbulb,
  Thermometer,
  Droplet,
  Lock,
} from "lucide-react";
import Link from "next/link";
import "../../lib/i18n";

export default function HomePage() {
  const { t } = useTranslation();
  const { loading: translationsLoading } = usePayloadTranslations();

  useEffect(() => {
    // Initialize i18n on client side
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <Header isTransparent={true} currentPage="home" />

      {/* Hero Section */}
      <section className="relative h-[80vh] flex items-center justify-center text-center text-white hero-gradient">
        <div className="absolute inset-0 bg-black opacity-50"></div>{" "}
        {/* Dark overlay */}
        <div className="relative z-10 space-y-8 px-8 max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-extralight tracking-wide leading-tight animate-fade-in-up">
            {t("home.hero.title")}
          </h1>
          <p className="text-xl md:text-2xl font-light leading-relaxed animate-fade-in-up animation-delay-200">
            {t("home.hero.subtitle")}
          </p>
          <Link href="/#contact">
            <Button
              size="lg"
              className="bg-primary hover:bg-primary-foreground text-primary-foreground hover:text-primary transition-all duration-300 text-lg font-light tracking-wider px-10 py-5 group"
            >
              {t("home.hero.cta")}
            </Button>
          </Link>
        </div>
      </section>

      {/* About Us Section */}
      <section id="about" className="py-32 bg-white">
        <div className="container mx-auto px-8 max-w-6xl">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-8 tracking-wide">
                {t("home.about.title")}
              </h2>
              <p className="text-slate-600 font-light text-lg leading-relaxed mb-10">
                {t("home.about.description")}
              </p>
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <ShieldCheck className="h-8 w-8 text-primary flex-shrink-0" />
                  <div>
                    <h3 className="text-xl font-light text-slate-800 mb-2">
                      {t("home.about.advantages.responsibility.title")}
                    </h3>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {t("home.about.advantages.responsibility.description")}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <Zap className="h-8 w-8 text-primary flex-shrink-0" />
                  <div>
                    <h3 className="text-xl font-light text-slate-800 mb-2">
                      {t("home.about.advantages.quality.title")}
                    </h3>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {t("home.about.advantages.quality.description")}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <Home className="h-8 w-8 text-primary flex-shrink-0" />
                  <div>
                    <h3 className="text-xl font-light text-slate-800 mb-2">
                      {t("home.about.advantages.certified.title")}
                    </h3>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {t("home.about.advantages.certified.description")}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <Lock className="h-8 w-8 text-primary flex-shrink-0" />
                  <div>
                    <h3 className="text-xl font-light text-slate-800 mb-2">
                      {t("home.about.advantages.knowledge.title")}
                    </h3>
                    <p className="text-slate-600 font-light leading-relaxed">
                      {t("home.about.advantages.knowledge.description")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative h-96 w-full">
              <img
                src="/modern-luxury-villa-with-security-system-elegant-a.jpg"
                alt="Про нас"
                className="absolute inset-0 w-full h-full object-cover shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* How We Work Section */}
      <section id="how-we-work" className="py-32 bg-slate-50">
        <div className="container mx-auto px-8 text-center max-w-6xl">
          <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-20 tracking-wide">
            {t("home.howWeWork.title")}
          </h2>
          <div className="relative flex justify-center items-center h-80">
            <div className="absolute flex justify-around w-full max-w-4xl">
              <div className="relative w-48 h-48 rounded-full border border-primary flex items-center justify-center bg-white shadow-lg animate-fade-in-left animation-delay-300">
                <span className="text-xl font-light text-slate-700 tracking-wider">
                  {t("home.howWeWork.steps.sale")}
                </span>
              </div>
              <div className="relative w-48 h-48 rounded-full border border-primary flex items-center justify-center bg-white shadow-lg animate-fade-in-up animation-delay-600">
                <span className="text-xl font-light text-slate-700 tracking-wider">
                  {t("home.howWeWork.steps.installation")}
                </span>
              </div>
              <div className="relative w-48 h-48 rounded-full border border-primary flex items-center justify-center bg-white shadow-lg animate-fade-in-right animation-delay-900">
                <span className="text-xl font-light text-slate-700 tracking-wider">
                  {t("home.howWeWork.steps.programming")}
                </span>
              </div>
            </div>
          </div>
          <p className="text-slate-600 font-light text-lg leading-relaxed max-w-3xl mx-auto mt-20">
            {t("home.howWeWork.description")}
          </p>
        </div>
      </section>

      {/* Security Systems Section */}
      <section id="security-systems" className="py-32 bg-background">
        <div className="container mx-auto px-8 max-w-6xl">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-8 tracking-wide">
              {t("home.securitySystems.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-primary to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed">
              {t("home.securitySystems.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-16 mb-20">
            <div>
              <h3 className="text-2xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.wiredWireless.title")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed mb-6">
                {t("home.securitySystems.wiredWireless.description")}
              </p>
              <ul className="space-y-3 text-slate-600 font-light">
                <li className="flex items-center">
                  <Lightbulb className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  {t("home.securitySystems.wiredWireless.wired")}
                </li>
                <li className="flex items-center">
                  <Zap className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  {t("home.securitySystems.wiredWireless.wireless")}
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-2xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.communication.title")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed mb-6">
                {t("home.securitySystems.communication.description")}
              </p>
              <ul className="space-y-3 text-slate-600 font-light">
                <li className="flex items-center">
                  <Building className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  GSM
                </li>
                <li className="flex items-center">
                  <Factory className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  WiFi
                </li>
                <li className="flex items-center">
                  <Video className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  LAN
                </li>
                <li className="flex items-center">
                  <ShieldCheck className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                  Jeweller
                </li>
              </ul>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Lock className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.antiBreakIn")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t("home.securitySystems.categories.antiBreakInDescription")}
              </p>
            </div>
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Thermometer className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.fireProtection")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t("home.securitySystems.categories.fireProtectionDescription")}
              </p>
            </div>
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Droplet className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.waterLeak")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t("home.securitySystems.categories.waterLeakDescription")}
              </p>
            </div>
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Video className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.videoSurveillance")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t(
                  "home.securitySystems.categories.videoSurveillanceDescription"
                )}
              </p>
            </div>
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Lightbulb className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.comfort")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t("home.securitySystems.categories.comfortDescription")}
              </p>
            </div>
            <div className="bg-white p-8 shadow-sm group hover:shadow-lg transition-shadow duration-300">
              <Zap className="h-10 w-10 text-primary mb-6 group-hover:text-primary-foreground transition-colors duration-300" />
              <h3 className="text-xl font-light text-slate-800 mb-4">
                {t("home.securitySystems.categories.automation")}
              </h3>
              <p className="text-slate-600 font-light leading-relaxed text-sm">
                {t("home.securitySystems.categories.automationDescription")}
              </p>
            </div>
          </div>
        </div>
      </section>

      <CaseStudiesSection />

      {/* Contact Section */}
      <section id="contact" className="py-24 bg-background">
        <div className="container mx-auto px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-extralight text-slate-800 mb-6 tracking-wide">
              {t("home.contact.title")}
            </h2>
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-primary to-transparent mx-auto mb-8"></div>
            <p className="text-slate-600 font-light text-lg max-w-2xl mx-auto leading-relaxed">
              {t("home.contact.subtitle")}
            </p>
          </div>
          <ContactForm />
        </div>
      </section>

      <footer className="bg-slate-800 text-white py-16">
        <div className="container mx-auto px-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <ShieldCheck className="h-6 w-6 text-primary" />
              <span className="text-lg font-light tracking-wide">
                DEFENCE system
              </span>
            </div>
            <div className="w-12 h-px bg-primary/40 mx-auto mb-6"></div>
            <p className="text-slate-400 font-light text-sm tracking-wide mb-6">
              {t("footer.partnerText")}
            </p>
          </div>
          <div className="text-center pt-8 border-t border-slate-700">
            <p className="text-slate-500 text-xs font-light tracking-wide">
              {t("footer.copyright")}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
