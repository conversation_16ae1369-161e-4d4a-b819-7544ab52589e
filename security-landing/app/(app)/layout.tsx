import type React from "react";
import type { Metadata } from "next";
import { Analytics } from "@vercel/analytics/next";
import { Suspense } from "react";
import "./globals.css";
import { generateStructuredData } from "@/lib/payload-client-types";
import { GoogleAnalytics } from "@/components/analytics";
import { ErrorBoundary } from "@/lib/error-boundary";

export async function generateMetadata(): Promise<Metadata> {
  const baseUrl = process.env.NEXT_PUBLIC_URL || "https://defencesystem.pl";

  // Fetch SEO data from Payload for the home page
  const { getSEOData } = await import("@/lib/payload");
  const seoData = await getSEOData("/", "pl");

  // Default fallback values
  const defaultTitle = "Defence System - Twoje Bezpieczeństwo, Nasz Priorytet";
  const defaultDescription =
    "Profesjonalne systemy bezpieczeństwa Ajax Systems. Ochrona antywłamaniowa, przeciwpożarowa, monitoring wideo. Oficjalny partner Ajax Systems w Polsce.";
  const defaultKeywords =
    "systemy bezpieczeństwa, Ajax Systems, monitoring, ochrona antywłamaniowa, czujniki ruchu, alarmy";

  const title = seoData?.metaTitle || defaultTitle;
  const description = seoData?.metaDescription || defaultDescription;
  const keywords = seoData?.keywords || defaultKeywords;
  const ogImage = seoData?.ogImage?.url || "/placeholder-logo.png";
  const canonicalUrl = seoData?.canonicalUrl || baseUrl;

  return {
    title,
    description,
    keywords,
    authors: [{ name: "Defence System" }],
    creator: "Defence System",
    publisher: "Defence System",
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        pl: "/pl",
        en: "/en",
      },
    },
    openGraph: {
      type: "website",
      locale: "pl_PL",
      url: canonicalUrl,
      siteName: "Defence System",
      title: seoData?.ogTitle || title,
      description: seoData?.ogDescription || description,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Defence System Logo",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: seoData?.ogTitle || title,
      description: seoData?.ogDescription || description,
      images: [ogImage],
    },
    robots: {
      index: !seoData?.noIndex,
      follow: !seoData?.noIndex,
      googleBot: {
        index: !seoData?.noIndex,
        follow: !seoData?.noIndex,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    verification: {
      google: "your-google-verification-code",
      // Add other verification codes as needed
    },
    other: seoData?.structuredData
      ? {
          "structured-data": JSON.stringify(seoData.structuredData),
        }
      : undefined,
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const gaId = process.env.NEXT_PUBLIC_GA_ID;

  return (
    <html lang="pl">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Century+Gothic:wght@100;200;300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(generateStructuredData("organization", {})),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(
              generateStructuredData("website", {
                name: "Defence System",
                description:
                  "Profesjonalne systemy bezpieczeństwa Ajax Systems",
              })
            ),
          }}
        />
      </head>
      <body className="font-sans antialiased">
        <ErrorBoundary>
          <Suspense
            fallback={
              <div className="min-h-screen flex items-center justify-center">
                <div className="animate-pulse text-slate-500">Loading...</div>
              </div>
            }
          >
            {children}
          </Suspense>
        </ErrorBoundary>
        <Analytics />
        {gaId && <GoogleAnalytics gaId={gaId} />}
      </body>
    </html>
  );
}
