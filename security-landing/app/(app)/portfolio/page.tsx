"use client";

import { useEffect, useState } from "react";
import { Header } from "@/components/header";
import { usePayloadTranslations } from "@/hooks/use-payload-translations";
import { getCaseStudiesServer } from "@/lib/payload-server";
import type { PayloadCaseStudy } from "@/lib/payload-client-types";
import { ArrowRight, MapPin, Calendar, User } from "lucide-react";
import Link from "next/link";

export default function PortfolioPage() {
  const { loading: translationsLoading } = usePayloadTranslations();
  const [caseStudies, setCaseStudies] = useState<PayloadCaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>("all");

  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        setLoading(true);
        const studies = await getCaseStudiesServer("ua"); // Using Ukrainian language
        setCaseStudies(studies);
      } catch (error) {
        console.error("Error fetching case studies:", error);
        // Fallback with static data
        setCaseStudies([
          {
            id: "1",
            title: "Безпека приватного будинку в Києві",
            language: "ua",
            category: "residential",
            location: "Київ, Україна",
            date: "2024-01-15",
            client: "Приватний клієнт",
            featuredImage: {
              id: "img1",
              url: "/modern-villa-with-security-system-installation-ele.jpg",
              alt: "Сучасна вілла з системою безпеки",
            },
            description:
              "Комплексна система безпеки для приватного будинку площею 350 кв.м.",
            status: "published",
            updatedAt: "2024-01-15T10:00:00Z",
            createdAt: "2024-01-15T10:00:00Z",
          },
          {
            id: "2",
            title: "Офісний центр з інтегрованою безпекою",
            language: "ua",
            category: "commercial",
            location: "Львів, Україна",
            date: "2024-02-20",
            client: "Бізнес-центр 'Галицький'",
            featuredImage: {
              id: "img2",
              url: "/modern-office-building-with-security-cameras-and-a.jpg",
              alt: "Сучасний офісний будинок",
            },
            description:
              "Повна інтеграція систем безпеки для 15-поверхового офісного центру.",
            status: "published",
            updatedAt: "2024-02-20T10:00:00Z",
            createdAt: "2024-02-20T10:00:00Z",
          },
          {
            id: "3",
            title: "Промислове підприємство - максимальний захист",
            language: "ua",
            category: "industrial",
            location: "Дніпро, Україна",
            date: "2024-03-10",
            client: "ТОВ 'Металург'",
            featuredImage: {
              id: "img3",
              url: "/modern-retail-store-interior-security-cameras-eleg.jpg",
              alt: "Промислове підприємство",
            },
            description:
              "Комплексний захист промислового об'єкта з периметральним контролем.",
            status: "published",
            updatedAt: "2024-03-10T10:00:00Z",
            createdAt: "2024-03-10T10:00:00Z",
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, []);

  const filteredCaseStudies =
    filter === "all"
      ? caseStudies
      : caseStudies.filter((study) => study.category === filter);

  const categoryLabels = {
    all: "Всі проекти",
    residential: "Житлові",
    commercial: "Комерційні",
    industrial: "Промислові",
  };

  return (
    <div className="min-h-screen bg-white">
      <Header isTransparent={false} currentPage="portfolio" />

      {/* Hero Section */}
      <section className="pt-32 pb-20 bg-gradient-to-br from-slate-900 via-slate-800 to-orange-900/30">
        <div className="container mx-auto px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-light text-white mb-6 tracking-wide">
            ПОРТФОЛІО
          </h1>
          <div className="w-16 h-px bg-gradient-to-r from-transparent via-orange-400 to-transparent mx-auto mb-8"></div>
          <p className="text-orange-100 font-light text-lg max-w-2xl mx-auto leading-relaxed">
            Наші реалізовані проекти у сфері безпеки
          </p>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-white border-b border-slate-200">
        <div className="container mx-auto px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {Object.entries(categoryLabels).map(([key, label]) => (
              <button
                key={key}
                onClick={() => setFilter(key)}
                className={`px-6 py-3 text-sm font-light tracking-wide transition-all duration-300 ${
                  filter === key
                    ? "bg-orange-500 text-white border border-orange-500"
                    : "bg-white text-slate-600 border border-slate-300 hover:border-orange-300 hover:text-orange-600"
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-20">
        <div className="container mx-auto px-8">
          {loading ? (
            <div className="text-center py-20">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-orange-500 border-r-transparent"></div>
              <p className="mt-4 text-slate-600 font-light">
                Завантаження проектів...
              </p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {filteredCaseStudies.map((study) => (
                <div key={study.id} className="group cursor-pointer">
                  <div className="bg-white border border-slate-200 hover:border-orange-300 transition-all duration-300 hover:shadow-lg overflow-hidden">
                    {/* Image */}
                    <div className="aspect-[4/3] overflow-hidden">
                      <img
                        src={study.featuredImage?.url || "/placeholder.jpg"}
                        alt={study.featuredImage?.alt || study.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        {study.category && (
                          <span className="text-xs font-medium text-orange-600 uppercase tracking-wider bg-orange-50 px-2 py-1">
                            {
                              categoryLabels[
                                study.category as keyof typeof categoryLabels
                              ]
                            }
                          </span>
                        )}
                        {study.date && (
                          <div className="flex items-center text-xs text-slate-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(study.date).toLocaleDateString("uk-UA")}
                          </div>
                        )}
                      </div>

                      <h3 className="text-xl font-medium text-slate-800 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                        {study.title}
                      </h3>

                      <p className="text-slate-600 font-light text-sm leading-relaxed mb-4 line-clamp-2">
                        {study.description}
                      </p>

                      <div className="space-y-2 text-xs text-slate-500">
                        {study.location && (
                          <div className="flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {study.location}
                          </div>
                        )}
                        {study.client && (
                          <div className="flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            {study.client}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-100">
                        <span className="text-orange-600 font-light text-sm group-hover:text-orange-700 transition-colors duration-300">
                          Детальніше
                        </span>
                        <ArrowRight className="h-4 w-4 text-orange-600 group-hover:text-orange-700 group-hover:translate-x-1 transition-all duration-300" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {!loading && filteredCaseStudies.length === 0 && (
            <div className="text-center py-20">
              <p className="text-slate-500 font-light">
                Проекти в цій категорії поки що відсутні
              </p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-800 to-orange-900/30">
        <div className="container mx-auto px-8 text-center">
          <h2 className="text-3xl font-light text-white mb-6 tracking-wide">
            Готові обговорити ваш проект?
          </h2>
          <p className="text-orange-100 font-light mb-8 max-w-2xl mx-auto">
            Кожен проект унікальний. Розкажіть нам про ваші потреби, і ми
            запропонуємо найкраще рішення.
          </p>
          <Link
            href="/#contact"
            className="inline-flex items-center bg-orange-500/20 backdrop-blur-sm text-white border border-orange-400/40 hover:bg-orange-500/30 hover:border-orange-400/60 transition-all duration-500 text-sm font-light tracking-wider px-8 py-4 group"
          >
            Отримати консультацію
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-800 text-white py-12">
        <div className="container mx-auto px-8 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <span className="text-lg font-light tracking-wide">
              DEFENCE system
            </span>
          </div>
          <p className="text-slate-400 font-light text-sm tracking-wide mb-4">
            Офіційний партнер Ajax Systems
          </p>
          <div className="text-center pt-6 border-t border-slate-700">
            <p className="text-slate-500 text-xs font-light tracking-wide">
              © 2024 Defence System. Всі права захищені.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
