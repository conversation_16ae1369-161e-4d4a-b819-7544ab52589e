import type { Metadata } from "next";
import { getSEOServer } from "@/lib/payload-server";

export async function generateMetadata(): Promise<Metadata> {
  const baseUrl = process.env.NEXT_PUBLIC_URL || "https://defencesystem.pl";

  // Fetch SEO data from Payload for the case studies page
  const seoData = await getSEOServer("/case-studies", "pl");

  // Default fallback values
  const defaultTitle = "Realizacje - Defence System";
  const defaultDescription =
    "Poznaj nasze realizacje systemów bezpieczeństwa Ajax Systems. Profesjonalne instalacje dla domów, biur i firm.";

  const title = seoData?.metaTitle || defaultTitle;
  const description = seoData?.metaDescription || defaultDescription;
  const keywords =
    seoData?.keywords ||
    "realizacje, systemy bezpieczeństwa, Ajax Systems, instalacje";
  const ogImage = seoData?.ogImage?.url || "/placeholder-logo.png";
  const canonicalUrl = seoData?.canonicalUrl || `${baseUrl}/case-studies`;

  return {
    title,
    description,
    keywords,
    authors: [{ name: "Defence System" }],
    creator: "Defence System",
    publisher: "Defence System",
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        pl: "/pl/case-studies",
        en: "/en/case-studies",
      },
    },
    openGraph: {
      type: "website",
      locale: "pl_PL",
      url: canonicalUrl,
      siteName: "Defence System",
      title: seoData?.ogTitle || title,
      description: seoData?.ogDescription || description,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Defence System Case Studies",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: seoData?.ogTitle || title,
      description: seoData?.ogDescription || description,
      images: [ogImage],
    },
    robots: {
      index: !seoData?.noIndex,
      follow: !seoData?.noIndex,
      googleBot: {
        index: !seoData?.noIndex,
        follow: !seoData?.noIndex,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    other: seoData?.structuredData
      ? {
          "structured-data": JSON.stringify(seoData.structuredData),
        }
      : undefined,
  };
}

export default function CaseStudiesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
