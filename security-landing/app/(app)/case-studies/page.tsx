"use client";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { SharedLayout } from "@/components/shared-layout";
import { ArrowLeft, Calendar, MapPin, Users } from "lucide-react";
import Link from "next/link";
import { getCaseStudiesServer } from "@/lib/payload-server";
import type { PayloadCaseStudy } from "@/lib/payload-client-types";
import { usePayloadTranslations } from "@/hooks/use-payload-translations";
import "../../../lib/i18n";

export default function CaseStudiesPage() {
  const { t, i18n } = useTranslation();
  const { loading: translationsLoading } = usePayloadTranslations();
  const [caseStudies, setCaseStudies] = useState<PayloadCaseStudy[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCaseStudies = async () => {
      try {
        const studies = await getCaseStudiesServer(
          i18n.language as "pl" | "en"
        );
        setCaseStudies(studies);
      } catch (error) {
        console.error("Error fetching case studies:", error);
        // Fallback to static data if Payload is not available
        setCaseStudies(staticCaseStudies);
      } finally {
        setLoading(false);
      }
    };

    fetchCaseStudies();
  }, [i18n.language]);

  // Static fallback data
  const staticCaseStudies: PayloadCaseStudy[] = [
    {
      id: "1",
      title: "Modern Villa Protection System",
      category: "Residential",
      location: "Warsaw, Poland",
      date: "2024-03-01",
      client: "Private Residence",
      featuredImage: {
        url: "/modern-luxury-villa-with-security-system-elegant-a.jpg",
      },
      description:
        "Complete security ecosystem for a 500m² modern villa featuring perimeter protection, advanced video surveillance, and seamless smart home integration.",
      challenge:
        "The client required a comprehensive security solution that would protect their family while maintaining the aesthetic appeal of their modern architectural design.",
      solution:
        "We implemented a multi-layered Ajax security system with invisible perimeter protection, discreet indoor sensors, and strategically placed cameras that blend with the villa's design.",
      results: [
        { result: "100% perimeter coverage without visual impact" },
        { result: "24/7 monitoring with instant mobile alerts" },
        { result: "Integration with existing smart home systems" },
        { result: "Zero false alarms in 6 months of operation" },
      ],
      language: i18n.language as "pl" | "en",
      status: "published" as const,
    },
    {
      id: "2",
      title: "Office Complex Security Upgrade",
      category: "Commercial",
      location: "Krakow, Poland",
      date: "2024-01-01",
      client: "Tech Company HQ",
      featuredImage: {
        url: "/modern-office-building-glass-facade-security-camer.jpg",
      },
      description:
        "Multi-zone security transformation for a 3-story office building housing 200+ employees with advanced access control and fire protection systems.",
      challenge:
        "Upgrading from an outdated security system while maintaining business operations and ensuring compliance with corporate security standards.",
      solution:
        "Phased installation of Ajax systems with zone-by-zone activation, integrated access control, and comprehensive fire protection with minimal business disruption.",
      results: [
        { result: "Seamless migration with zero downtime" },
        { result: "50% reduction in security incidents" },
        { result: "Automated access control for 200+ employees" },
        { result: "Full compliance with corporate security policies" },
      ],
      language: i18n.language as "pl" | "en",
      status: "published" as const,
    },
    {
      id: "3",
      title: "Retail Chain Protection Network",
      category: "Retail",
      location: "Multiple Locations",
      date: "2023-12-01",
      client: "Fashion Retail Chain",
      featuredImage: {
        url: "/modern-retail-store-interior-security-cameras-eleg.jpg",
      },
      description:
        "Standardized security solution across 15 retail locations with centralized monitoring and inventory protection systems.",
      challenge:
        "Creating a unified security standard across multiple locations while addressing unique requirements of each store layout and local regulations.",
      solution:
        "Developed a modular Ajax system template that could be customized for each location while maintaining centralized monitoring and management capabilities.",
      results: [
        { result: "85% reduction in theft incidents" },
        { result: "Centralized monitoring of all 15 locations" },
        { result: "Standardized response protocols" },
        { result: "ROI achieved within 8 months" },
      ],
      language: i18n.language as "pl" | "en",
      status: "published" as const,
    },
  ];

  return (
    <SharedLayout isTransparent={false} currentPage="case-studies">
      {/* Content */}

      <section className="py-32 bg-slate-800 relative overflow-hidden">
        <div className="absolute inset-0 hero-gradient"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        <div className="container mx-auto px-8 text-center relative z-10">
          <Link href="/">
            <Button
              variant="ghost"
              className="text-white/80 hover:text-white hover:bg-white/10 mb-12 text-sm font-light tracking-wide rounded-none group transition-all duration-300"
            >
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-300" />
              BACK TO HOME
            </Button>
          </Link>
          <h1 className="text-5xl md:text-6xl font-extralight text-white mb-8 tracking-wide text-balance">
            CASE STUDIES
          </h1>
          <div className="w-20 h-px bg-white/40 mx-auto mb-8"></div>
          <p className="text-white/80 font-light text-lg max-w-2xl mx-auto leading-relaxed">
            Real projects, real results — discover how we protect what matters
            most to our clients
          </p>
        </div>
      </section>

      <section className="py-32 bg-white">
        <div className="container mx-auto px-8">
          {loading ? (
            <div className="flex justify-center py-20">
              <div className="animate-pulse text-slate-500">
                Loading case studies...
              </div>
            </div>
          ) : (
            <div className="space-y-32">
              {caseStudies.map((study, index) => (
                <article key={study.id} className="max-w-4xl mx-auto group">
                  <div className="aspect-[16/10] mb-12 overflow-hidden bg-gradient-to-br from-slate-100 to-blue-100/30 border border-slate-200/50">
                    <img
                      src={study.featuredImage?.url || "/placeholder.svg"}
                      alt={study.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                  </div>

                  <div className="space-y-8">
                    <div className="flex flex-wrap items-center gap-4 text-sm text-slate-500 font-light">
                      <div className="flex items-center hover:text-slate-700 transition-colors">
                        <Calendar className="h-4 w-4 mr-2" />
                        {new Date(study.date).toLocaleDateString(
                          i18n.language === "pl" ? "pl-PL" : "en-US",
                          {
                            year: "numeric",
                            month: "long",
                          }
                        )}
                      </div>
                      <div className="flex items-center hover:text-slate-700 transition-colors">
                        <MapPin className="h-4 w-4 mr-2" />
                        {study.location}
                      </div>
                      <div className="flex items-center hover:text-slate-700 transition-colors">
                        <Users className="h-4 w-4 mr-2" />
                        {study.client}
                      </div>
                      <span className="px-4 py-2 bg-slate-100 text-slate-600 text-xs tracking-wider uppercase border border-slate-200/50">
                        {study.category}
                      </span>
                    </div>

                    <h2 className="text-3xl md:text-4xl font-light text-slate-800 tracking-wide text-balance">
                      {study.title}
                    </h2>

                    <p className="text-slate-600 font-light text-lg leading-relaxed">
                      {study.description}
                    </p>

                    <div className="grid md:grid-cols-2 gap-12 pt-8">
                      <div>
                        <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">
                          Challenge
                        </h3>
                        <p className="text-slate-600 font-light leading-relaxed">
                          {study.challenge}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">
                          Solution
                        </h3>
                        <p className="text-slate-600 font-light leading-relaxed">
                          {study.solution}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-light text-slate-800 mb-6 tracking-wide">
                        Results
                      </h3>
                      <ul className="space-y-3">
                        {study.results.map((result, resultIndex) => (
                          <li
                            key={resultIndex}
                            className="flex items-start group/item"
                          >
                            <div className="w-1.5 h-1.5 bg-slate-400 rounded-full mt-2.5 mr-4 flex-shrink-0 group-hover/item:bg-coral transition-colors duration-300"></div>
                            <span className="text-slate-600 font-light leading-relaxed">
                              {result.result}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {index < caseStudies.length - 1 && (
                    <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-200 to-transparent mt-20"></div>
                  )}
                </article>
              ))}
            </div>
          )}
        </div>
      </section>
    </SharedLayout>
  );
}
