# Deployment Guide - Defence System Website

## 🚀 Production Deployment

### Prerequisites

- Node.js 18+
- pnpm package manager
- Database (SQLite for development, PostgreSQL/MongoDB for production)
- Domain and hosting platform

### Environment Configuration

Create `.env.local` from `.env.example`:

```bash
# Payload CMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-here
DATABASE_URI=postgresql://user:password@host:port/database

# Public URLs
NEXT_PUBLIC_URL=https://yourdomain.com
NEXT_PUBLIC_PAYLOAD_URL=https://yourdomain.com

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
VERCEL_ANALYTICS_ID=your-vercel-analytics-id

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SEO
GOOGLE_VERIFICATION_CODE=your-google-verification-code
```

### Database Setup

#### PostgreSQL (Recommended for Production)

```bash
# Install PostgreSQL adapter
pnpm add @payloadcms/db-postgres

# Update payload.config.ts
import { postgresAdapter } from '@payloadcms/db-postgres'

db: postgresAdapter({
  pool: {
    connectionString: process.env.DATABASE_URI,
  },
}),
```

#### MongoDB Alternative

```bash
# Install MongoDB adapter
pnpm add @payloadcms/db-mongodb

# Update payload.config.ts
import { mongooseAdapter } from '@payloadcms/db-mongodb'

db: mongooseAdapter({
  url: process.env.DATABASE_URI,
}),
```

### Vercel Deployment

1. **Connect Repository**

   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Deploy
   vercel --prod
   ```

2. **Environment Variables**
   Set all variables in Vercel dashboard or via CLI:

   ```bash
   vercel env add PAYLOAD_SECRET
   vercel env add DATABASE_URI
   # ... add all other variables
   ```

3. **Build Settings**
   ```json
   {
     "buildCommand": "pnpm build",
     "outputDirectory": ".next",
     "installCommand": "pnpm install",
     "framework": "nextjs"
   }
   ```

### Other Hosting Platforms

#### Railway

```bash
# Install Railway CLI
npm install -g @railway/cli

# Deploy
railway login
railway link
railway up
```

#### DigitalOcean App Platform

1. Connect GitHub repository
2. Set environment variables
3. Configure build settings:
   - Build Command: `pnpm build`
   - Run Command: `pnpm start`

#### AWS/Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install -g pnpm && pnpm install
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

### Post-Deployment Setup

1. **Admin User Creation**
   Visit `https://yourdomain.com/admin` and create first admin user

2. **Content Population**

   - Add translations in Payload admin
   - Configure SEO for each page
   - Upload case studies with images
   - Test contact form functionality

3. **SEO Configuration**

   - Submit sitemap to Google Search Console: `yourdomain.com/sitemap.xml`
   - Verify domain ownership
   - Set up Google Analytics
   - Configure social media meta tags

4. **Performance Monitoring**
   - Enable Vercel Analytics
   - Set up error tracking (Sentry recommended)
   - Monitor Core Web Vitals
   - Configure caching headers

### SSL and Security

1. **SSL Certificate**

   - Automatic with Vercel/Netlify
   - Manual setup for other platforms

2. **Security Headers**
   Already configured in `next.config.mjs`:

   - Content Security Policy
   - XSS Protection
   - Frame Options
   - HSTS Headers

3. **Rate Limiting**
   Configure API rate limiting for contact forms:
   ```typescript
   // Implement in API routes
   const rateLimit = new Map();
   // Check rate limits before processing
   ```

### Backup Strategy

1. **Database Backups**

   ```bash
   # PostgreSQL
   pg_dump $DATABASE_URI > backup.sql

   # MongoDB
   mongodump --uri $DATABASE_URI
   ```

2. **Media Files**
   - Use cloud storage (AWS S3, Cloudinary)
   - Regular automated backups
   - CDN integration for performance

### Monitoring and Maintenance

1. **Uptime Monitoring**

   - Use services like UptimeRobot
   - Monitor `/api/health` endpoint

2. **Performance Monitoring**

   - Core Web Vitals tracking
   - Bundle size monitoring
   - API response times

3. **Regular Updates**

   ```bash
   # Update dependencies monthly
   pnpm update

   # Security audits
   pnpm audit
   ```

### Troubleshooting

#### Common Issues

1. **Build Failures**

   - Check Node.js version compatibility
   - Clear `.next` cache: `rm -rf .next`
   - Verify all environment variables

2. **Database Connection**

   - Verify DATABASE_URI format
   - Check network connectivity
   - Ensure database exists

3. **Payload Admin Access**
   - Verify PAYLOAD_SECRET is set
   - Check admin route is not blocked
   - Clear browser cache

#### Error Logging

```typescript
// Add to payload.config.ts
onError: (error) => {
  console.error('Payload Error:', error);
  // Send to error tracking service
},
```

### Performance Optimization

1. **Caching Strategy**

   - API responses cached for 15-30 minutes
   - Static assets cached for 1 year
   - Database query optimization

2. **Image Optimization**

   - Next.js automatic optimization
   - WebP format support
   - Responsive image loading

3. **Bundle Optimization**
   - Tree shaking enabled
   - Code splitting by route
   - Dynamic imports for heavy components

## 🎯 Go Live Checklist

- [ ] Environment variables configured
- [ ] Database connected and populated
- [ ] Admin user created
- [ ] All translations added
- [ ] SEO data configured for all pages
- [ ] Contact form tested
- [ ] SSL certificate active
- [ ] Analytics tracking working
- [ ] Performance score > 90
- [ ] Mobile responsiveness verified
- [ ] Cross-browser testing completed
- [ ] Backup strategy implemented
- [ ] Monitoring tools configured

Your Defence System website is now production-ready! 🚀
