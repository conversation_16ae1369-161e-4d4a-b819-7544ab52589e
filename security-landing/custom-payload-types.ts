/* Custom Payload Types - Generated manually due to Node.js version incompatibility */

// Re-export existing types from payload-types
export * from "./payload-types";

// Custom collection types based on our collection definitions
export interface Translation {
  id: string;
  key: string;
  polish: string;
  english: string;
  category?: string | null;
  updatedAt: string;
  createdAt: string;
}

export interface Seo {
  id: string;
  pageName: string;
  slug: string;
  language: "pl" | "en";
  metaTitle?: string | null;
  metaDescription?: string | null;
  keywords?: string | null;
  ogImage?: string | null;
  ogTitle?: string | null;
  ogDescription?: string | null;
  canonicalUrl?: string | null;
  noIndex?: boolean | null;
  structuredData?: string | null;
  updatedAt: string;
  createdAt: string;
}

export interface CaseStudy {
  id: string;
  title: string;
  language: "pl" | "en";
  category?: "residential" | "commercial" | "industrial" | null;
  location?: string | null;
  date?: string | null;
  client?: string | null;
  featuredImage?: {
    id: string;
    url?: string | null;
    alt: string;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
  } | null;
  description?: string | null;
  challenge?: string | null;
  solution?: string | null;
  results?: Array<{
    id?: string | null;
    result?: string | null;
  }> | null;
  additionalImages?: Array<{
    id?: string | null;
    image?: {
      id: string;
      url?: string | null;
      alt: string;
      filename?: string | null;
      mimeType?: string | null;
      filesize?: number | null;
      width?: number | null;
      height?: number | null;
    } | null;
  }> | null;
  status?: "draft" | "published" | null;
  updatedAt: string;
  createdAt: string;
}

export interface Contact {
  id: string;
  fullName: string;
  email: string;
  phone?: string | null;
  message: string;
  subject?: string | null;
  status?: "new" | "in-progress" | "completed" | "cancelled" | null;
  priority?: "low" | "medium" | "high" | "urgent" | null;
  source?: "website" | "phone" | "email" | "referral" | "other" | null;
  notes?: string | null;
  followUpDate?: string | null;
  assignedTo?: {
    relationTo: "users";
    value: string | import("./payload-types").User;
  } | null;
  updatedAt: string;
  createdAt: string;
}

// Collection names for type safety
export type CollectionSlug =
  | "users"
  | "media"
  | "translations"
  | "seo"
  | "case-studies"
  | "contacts";

// Extended Config interface
declare module "payload" {
  export interface GeneratedTypes {
    collections: {
      users: import("./payload-types").User;
      media: import("./payload-types").Media;
      translations: Translation;
      seo: Seo;
      "case-studies": CaseStudy;
      contacts: Contact;
      "payload-locked-documents": import("./payload-types").PayloadLockedDocument;
      "payload-preferences": import("./payload-types").PayloadPreference;
      "payload-migrations": import("./payload-types").PayloadMigration;
    };
  }
}
